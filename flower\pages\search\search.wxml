<!--search.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-header">
    <view class="search-input-container">
      <image class="search-icon" src="/images/search.png"></image>
      <input class="search-input" 
             placeholder="搜索你喜欢的礼品" 
             value="{{keyword}}" 
             bindinput="onKeywordChange"
             bindconfirm="onSearch"
             focus="{{true}}"
             confirm-type="search" />
      <text class="cancel-btn" bindtap="onCancel">取消</text>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{!keyword && searchHistory.length > 0}}">
    <view class="history-header">
      <text class="history-title">搜索历史</text>
      <text class="clear-btn" bindtap="onClearHistory">清空</text>
    </view>
    <view class="history-tags">
      <text class="history-tag" 
            wx:for="{{searchHistory}}" 
            wx:key="*this"
            bindtap="onHistoryTap"
            data-keyword="{{item}}">{{item}}</text>
    </view>
  </view>

  <!-- 热门搜索 -->
  <view class="hot-search" wx:if="{{!keyword}}">
    <view class="hot-title">热门搜索</view>
    <view class="hot-tags">
      <text class="hot-tag" 
            wx:for="{{hotKeywords}}" 
            wx:key="*this"
            bindtap="onHotTap"
            data-keyword="{{item}}">{{item}}</text>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{keyword && searchResults.length > 0}}">
    <view class="result-header">
      <text class="result-count">找到 {{totalCount}} 个相关商品</text>
    </view>
    <view class="product-list">
      <view class="product-item" 
            wx:for="{{searchResults}}" 
            wx:key="id"
            bindtap="onProductTap"
            data-product="{{item}}">
        <image class="product-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-desc">{{item.description}}</text>
          <view class="product-price">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.price}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{keyword && searchResults.length === 0 && !loading}}">
    <image class="empty-icon" src="/images/empty-search.png"></image>
    <text class="empty-text">没有找到相关商品</text>
    <text class="empty-tip">试试其他关键词吧</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">搜索中...</text>
  </view>
</view>
