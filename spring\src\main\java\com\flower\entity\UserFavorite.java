package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

@TableName("user_favorites")
public class UserFavorite {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private Long userId;
  
  private Long flowerId;
  
  private LocalDateTime createdAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setUserId(Long userId) {
    this.userId = userId;
  }
  
  public void setFlowerId(Long flowerId) {
    this.flowerId = flowerId;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public String toString() {
    return "UserFavorite(id=" + getId() + ", userId=" + getUserId() + ", flowerId=" + getFlowerId() + ", createdAt=" + getCreatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.UserFavorite))
      return false; 
    com.flower.entity.UserFavorite other = (com.flower.entity.UserFavorite)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$userId = getUserId(), other$userId = other.getUserId();
    if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId))
      return false; 
    Object this$flowerId = getFlowerId(), other$flowerId = other.getFlowerId();
    if ((this$flowerId == null) ? (other$flowerId != null) : !this$flowerId.equals(other$flowerId))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    return !((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.UserFavorite;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $userId = getUserId();
    result = result * 59 + (($userId == null) ? 43 : $userId.hashCode());
    Object $flowerId = getFlowerId();
    result = result * 59 + (($flowerId == null) ? 43 : $flowerId.hashCode());
    Object $createdAt = getCreatedAt();
    return result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public Long getUserId() {
    return this.userId;
  }
  
  public Long getFlowerId() {
    return this.flowerId;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
}
