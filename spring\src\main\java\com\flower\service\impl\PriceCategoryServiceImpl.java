package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.entity.PriceCategory;
import com.flower.mapper.PriceCategoryMapper;
import com.flower.service.PriceCategoryService;
import java.lang.invoke.SerializedLambda;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class PriceCategoryServiceImpl implements PriceCategoryService {
  @Autowired
  private PriceCategoryMapper priceCategoryMapper;
  
  public PageResult<PriceCategory> getPriceCategoriesByPage(Long current, Long size, String keyword, Integer status) {
    Page<PriceCategory> page = new Page<>(current.longValue(), size.longValue());
    LambdaQueryWrapper<PriceCategory> wrapper = new LambdaQueryWrapper<>();
    if (StringUtils.hasText(keyword))
      wrapper.and(w -> w.like(PriceCategory::getName, keyword).or().like(PriceCategory::getDescription, keyword));
    if (status != null)
      wrapper.eq(PriceCategory::getStatus, status); 
    wrapper.orderByAsc(PriceCategory::getSortOrder);
    wrapper.orderByDesc(PriceCategory::getCreatedAt);
    IPage<PriceCategory> result = this.priceCategoryMapper.selectPage(page, wrapper);
    return PageResult.of(result.getRecords(), Long.valueOf(result.getTotal()), Long.valueOf(result.getSize()), Long.valueOf(result.getCurrent()));
  }
  
  public PriceCategory getPriceCategoryById(Long id) {
    return (PriceCategory)this.priceCategoryMapper.selectById(id);
  }
  
  public boolean createPriceCategory(PriceCategory priceCategory) {
    priceCategory.setCreatedAt(LocalDateTime.now());
    priceCategory.setUpdatedAt(LocalDateTime.now());
    if (priceCategory.getStatus() == null)
      priceCategory.setStatus(Integer.valueOf(1)); 
    if (priceCategory.getSortOrder() == null)
      priceCategory.setSortOrder(Integer.valueOf(0)); 
    return (this.priceCategoryMapper.insert(priceCategory) > 0);
  }
  
  public boolean updatePriceCategory(PriceCategory priceCategory) {
    priceCategory.setUpdatedAt(LocalDateTime.now());
    return (this.priceCategoryMapper.updateById(priceCategory) > 0);
  }
  
  public boolean deletePriceCategory(Long id) {
    return (this.priceCategoryMapper.deleteById(id) > 0);
  }
  
  public boolean updatePriceCategoryStatus(Long id, Integer status) {
    PriceCategory priceCategory = new PriceCategory();
    priceCategory.setId(id);
    priceCategory.setStatus(status);
    priceCategory.setUpdatedAt(LocalDateTime.now());
    return (this.priceCategoryMapper.updateById(priceCategory) > 0);
  }
  
  public List<PriceCategory> getAllActivePriceCategories() {
    LambdaQueryWrapper<PriceCategory> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(PriceCategory::getStatus, Integer.valueOf(1));
    wrapper.orderByAsc(PriceCategory::getSortOrder);
    return this.priceCategoryMapper.selectList(wrapper);
  }
  
  public PriceCategory getPriceCategoryByPrice(BigDecimal price) {
    LambdaQueryWrapper<PriceCategory> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(PriceCategory::getStatus, Integer.valueOf(1));
    wrapper.le(PriceCategory::getMinPrice, price);
    wrapper.ge(PriceCategory::getMaxPrice, price);
    wrapper.orderByAsc(PriceCategory::getSortOrder);
    wrapper.last("LIMIT 1");
    return this.priceCategoryMapper.selectOne(wrapper);
  }
}
