<!--index.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-container">
    <view class="search-box" bindtap="onSearchTap">
      <image class="search-icon" src="/images/search.png"></image>
      <text class="search-placeholder">搜索你喜欢的礼品</text>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="swiper-container">
    <swiper class="swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}">
      <swiper-item wx:for="{{swiperList}}" wx:key="id" bindtap="onSwiperTap" data-item="{{item}}">
        <image class="swiper-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
  </view>

  <!-- 家纺礼品专区 -->
  <view class="textile-section">
    <view class="textile-banner">
      <view class="textile-content">
        <view class="textile-title">
          <image class="crown-icon" src="/images/crown.png"></image>
          <text class="title-text">家纺礼品专区</text>
        </view>
        <text class="subtitle">Home Vica Department</text>
      </view>
      <view class="textile-tag">家纺</view>
    </view>
  </view>

  <!-- 礼品分类 -->
  <view class="category-section">
    <view class="section-title">礼品分类</view>
    <view class="category-grid">
      <view class="category-item" wx:for="{{categoryList}}" wx:key="id" bindtap="onCategoryTap" data-category="{{item}}">
        <image class="category-icon" src="{{item.icon}}" mode="aspectFit"></image>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 精选推荐 -->
  <view class="recommend-section">
    <view class="section-header">
      <view class="section-title">
        <image class="star-icon" src="/images/star.png"></image>
        <text>精选推荐</text>
      </view>
      <view class="more-link" bindtap="onMoreTap">
        <text>更多</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
    </view>
    <text class="section-subtitle">为你精心挑选的优质礼品</text>

    <view class="product-list">
      <view class="product-item" wx:for="{{productList}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
        <view class="product-image-container">
          <image class="product-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
          <view class="product-tag" wx:if="{{item.tag}}">{{item.tag}}</view>
        </view>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-desc">{{item.description}}</text>
          <view class="product-price">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.price}}</text>
            <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
          </view>
          <view class="product-tags">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
          </view>
          <view class="product-footer">
            <text class="stock-info">已售{{item.soldCount || 0}}件</text>
            <view class="cart-btn" bindtap="onAddToCart" data-product="{{item}}" catchtap="true">
              <image class="cart-icon" src="/images/cart-add.png"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
