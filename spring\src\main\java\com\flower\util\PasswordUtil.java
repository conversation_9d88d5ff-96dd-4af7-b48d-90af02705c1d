package com.flower.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

public class PasswordUtil {
  private static final PasswordEncoder passwordEncoder = (PasswordEncoder)new BCryptPasswordEncoder();
  
  public static String encode(String rawPassword) {
    return passwordEncoder.encode(rawPassword);
  }
  
  public static boolean matches(String rawPassword, String encodedPassword) {
    return passwordEncoder.matches(rawPassword, encodedPassword);
  }
  
  public static String generateRandomPassword(int length) {
    String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    StringBuilder password = new StringBuilder();
    for (int i = 0; i < length; i++) {
      int index = (int)(Math.random() * chars.length());
      password.append(chars.charAt(index));
    } 
    return password.toString();
  }
}
