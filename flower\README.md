# 恒通礼业微信小程序

## 项目简介
这是一个基于微信小程序开发的礼品商城应用，主要功能包括商品展示、分类浏览、搜索、购物车等。

## 已完成功能

### 1. 主页功能
- ✅ 搜索框（点击跳转到搜索页面）
- ✅ 轮播图展示（调用后端接口获取数据）
- ✅ 家纺礼品专区展示
- ✅ 8个分类网格展示（年会礼品、商务礼品、婚庆礼品等）
- ✅ 精选推荐商品列表
- ✅ 添加到购物车功能

### 2. 搜索功能
- ✅ 搜索页面
- ✅ 搜索历史记录
- ✅ 热门搜索推荐
- ✅ 搜索结果展示

### 3. 底部导航
- ✅ 首页、分类、购物车、我的四个Tab
- ✅ 导航图标和选中状态

### 4. 基础配置
- ✅ API接口配置（域名：https://mxm.qiangs.xyz/api）
- ✅ HTTP请求工具封装
- ✅ 常量配置文件
- ✅ 全局样式文件

## 技术栈
- 微信小程序原生开发
- ES6+ JavaScript
- WXSS样式
- 后端API接口对接

## 项目结构
```
flower/
├── app.js                 # 小程序入口文件
├── app.json               # 小程序配置文件
├── app.wxss               # 全局样式文件
├── config/                # 配置文件目录
│   ├── api.js            # API接口配置
│   └── constants.js      # 常量配置
├── utils/                 # 工具函数目录
│   ├── request.js        # HTTP请求工具
│   └── util.js           # 通用工具函数
├── pages/                 # 页面目录
│   ├── index/            # 主页
│   ├── category/         # 分类页（待实现）
│   ├── cart/             # 购物车页（待实现）
│   ├── profile/          # 我的页面（待实现）
│   └── search/           # 搜索页
└── images/               # 图片资源目录
```

## 后端接口
项目使用的主要API接口：

### 轮播图接口
- `GET /swiper/active` - 获取活跃的轮播图

### 商品接口
- `GET /flower/categories` - 获取商品分类
- `GET /flower/list` - 获取商品列表（支持分页、搜索、分类筛选）

### 购物车接口
- `POST /cart/add` - 添加商品到购物车
- `GET /cart/list/{userId}` - 获取用户购物车列表

## 待实现功能
1. 分类页面详细实现
2. 购物车页面功能
3. 用户个人中心页面
4. 商品详情页面
5. 订单管理功能
6. 用户登录/注册功能

## 注意事项
1. 需要添加相应的图片资源到 `/images/` 目录
2. 部分功能使用了模拟用户ID（userId: 1），实际使用时需要实现用户登录功能
3. 请确保后端服务正常运行在 https://mxm.qiangs.xyz/api

## 开发建议
1. 建议先完善图片资源，确保UI展示效果
2. 可以根据实际需求调整API接口参数
3. 建议添加错误处理和加载状态提示
4. 可以考虑添加商品收藏、评价等功能
