package com.flower.service;

import com.flower.entity.City;
import com.flower.entity.District;
import com.flower.entity.Province;
import java.util.List;
import java.util.Map;

public interface RegionService {
  List<Province> getAllProvinces();
  
  List<City> getCitiesByProvinceCode(String paramString);
  
  List<District> getDistrictsByCityCode(String paramString);
  
  Map<String, Object> getRegionData();
  
  Map<String, String> getRegionCodes(String paramString1, String paramString2, String paramString3);
  
  void clearAllRegionCache();
}
