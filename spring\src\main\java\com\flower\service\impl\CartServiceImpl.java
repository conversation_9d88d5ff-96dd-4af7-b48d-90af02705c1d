package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.CartItem;
import com.flower.mapper.CartItemMapper;
import com.flower.mapper.FlowerMapper;
import com.flower.mapper.UserMapper;
import com.flower.service.CartService;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.List;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CartServiceImpl implements CartService {
  @Autowired
  private CartItemMapper cartItemMapper;

  @Autowired
  private UserMapper userMapper;

  @Autowired
  private FlowerMapper flowerMapper;

  public CartItem addToCart(Long userId, Long flowerId, Integer quantity) {
    try {
      if (userId == null || flowerId == null || quantity == null || quantity.intValue() <= 0)
        throw new IllegalArgumentException("参数不能为空且数量必须大于0");
      if (this.userMapper.selectById(userId) == null)
        throw new IllegalArgumentException("用户不存在，用户ID: " + userId);
      if (this.flowerMapper.selectById(flowerId) == null)
        throw new IllegalArgumentException("花卉不存在，花卉ID: " + flowerId);
      LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(CartItem::getUserId, userId);
      wrapper.eq(CartItem::getFlowerId, flowerId);
      CartItem existingItem = (CartItem)this.cartItemMapper.selectOne((Wrapper)wrapper);
      if (existingItem != null) {
        existingItem.setQuantity(Integer.valueOf(existingItem.getQuantity().intValue() + quantity.intValue()));
        existingItem.setUpdatedAt(LocalDateTime.now());
        int i = this.cartItemMapper.updateById(existingItem);
        if (i > 0)
          return existingItem;
        throw new RuntimeException("更新购物车失败");
      }
      CartItem cartItem = new CartItem();
      cartItem.setUserId(userId);
      cartItem.setFlowerId(flowerId);
      cartItem.setQuantity(quantity);
      cartItem.setCreatedAt(LocalDateTime.now());
      cartItem.setUpdatedAt(LocalDateTime.now());
      int result = this.cartItemMapper.insert(cartItem);
      if (result > 0)
        return cartItem;
      throw new RuntimeException("添加到购物车失败");
    } catch (Exception e) {
      throw new RuntimeException("添加到购物车失败: " + e.getMessage(), e);
    }
  }

  public CartItem updateCartItem(Long userId, Long flowerId, Integer quantity) {
    LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(CartItem::getUserId, userId);
    wrapper.eq(CartItem::getFlowerId, flowerId);
    CartItem cartItem = (CartItem)this.cartItemMapper.selectOne((Wrapper)wrapper);
    if (cartItem != null) {
      cartItem.setQuantity(quantity);
      cartItem.setUpdatedAt(LocalDateTime.now());
      this.cartItemMapper.updateById(cartItem);
    }
    return cartItem;
  }

  public Boolean removeFromCart(Long userId, Long flowerId) {
    LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(CartItem::getUserId, userId);
    wrapper.eq(CartItem::getFlowerId, flowerId);
    int result = this.cartItemMapper.delete((Wrapper)wrapper);
    return Boolean.valueOf((result > 0));
  }

  public List<CartItem> getUserCartItems(Long userId) {
    LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(CartItem::getUserId, userId);
    wrapper.orderByDesc(CartItem::getCreatedAt);
    return this.cartItemMapper.selectList((Wrapper)wrapper);
  }

  public Boolean clearCart(Long userId) {
    LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(CartItem::getUserId, userId);
    int result = this.cartItemMapper.delete((Wrapper)wrapper);
    return Boolean.valueOf((result >= 0));
  }

  public Integer batchRemoveFromCart(Long userId, List<Long> flowerIds) {
    if (flowerIds == null || flowerIds.isEmpty())
      return Integer.valueOf(0);
    LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(CartItem::getUserId, userId)
      .in((SFunction<CartItem, ?>) CartItem::getFlowerId, flowerIds);
    int result = this.cartItemMapper.delete((Wrapper)wrapper);
    return Integer.valueOf(result);
  }

  public Integer getCartItemCount(Long userId) {
    LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(CartItem::getUserId, userId);
    List<CartItem> items = this.cartItemMapper.selectList((Wrapper)wrapper);
    return Integer.valueOf(items.stream().mapToInt(CartItem::getQuantity).sum());
  }
}
