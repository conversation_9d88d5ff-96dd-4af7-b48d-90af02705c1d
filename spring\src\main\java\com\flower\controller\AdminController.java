package com.flower.controller;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.common.Result;
import com.flower.entity.AdminUser;
import com.flower.entity.Category;
import com.flower.entity.City;
import com.flower.entity.District;
import com.flower.entity.Flower;
import com.flower.entity.FlowerReview;
import com.flower.entity.Order;
import com.flower.entity.OrderItem;
import com.flower.entity.PriceCategory;
import com.flower.entity.Province;
import com.flower.entity.User;
import com.flower.mapper.AdminUserMapper;
import com.flower.mapper.CategoryMapper;
import com.flower.mapper.FlowerMapper;
import com.flower.mapper.FlowerReviewMapper;
import com.flower.mapper.OrderItemMapper;
import com.flower.mapper.OrderMapper;
import com.flower.mapper.RegionMapper;
import com.flower.mapper.UserMapper;
import com.flower.service.AdminService;
import com.flower.service.CaptchaService;
import com.flower.service.FileUploadService;
import com.flower.service.OrderService;
import com.flower.service.PriceCategoryService;
import com.flower.util.PasswordUtil;
import com.flower.vo.FlowerVO;
import java.io.File;
import java.lang.invoke.SerializedLambda;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping({"/admin"})
@CrossOrigin(originPatterns = {"*"}, allowedHeaders = {"*"}, allowCredentials = "true")
public class AdminController {
  private static final Logger log = LoggerFactory.getLogger(com.flower.controller.AdminController.class);
  
  @Autowired
  private AdminService adminService;
  
  @Autowired
  private PriceCategoryService priceCategoryService;
  
  @Autowired
  private OrderService orderService;
  
  @Autowired
  private FileUploadService fileUploadService;
  
  @Autowired
  private AdminUserMapper adminUserMapper;
  
  @Autowired
  private UserMapper userMapper;
  
  @Autowired
  private FlowerMapper flowerMapper;
  
  @Autowired
  private OrderMapper orderMapper;
  
  @Autowired
  private OrderItemMapper orderItemMapper;
  
  @Autowired
  private CategoryMapper categoryMapper;
  
  @Autowired
  private FlowerReviewMapper flowerReviewMapper;
  
  @Autowired
  private RegionMapper regionMapper;
  
  @Autowired
  private CaptchaService captchaService;
  
  @GetMapping({"/captcha"})
  public Result<Map<String, String>> generateCaptcha() {
    try {
      com.flower.service.CaptchaResponse captcha = this.captchaService.generateCaptcha();
      Map<String, String> result = new HashMap<>();
      result.put("captchaId", captcha.getCaptchaId());
      result.put("image", captcha.getImage());
      return Result.success("验证码生成成功", result);
    } catch (Exception e) {
      log.error("生成验证码失败", e);
      return Result.error("生成验证码失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/login"})
  public Result<Map<String, Object>> login(@RequestBody Map<String, String> params, HttpServletRequest request) {
    try {
      String username = params.get("username");
      String password = params.get("password");
      String captchaId = params.get("captchaId");
      String captchaCode = params.get("captchaCode");
      if (username == null || username.trim().isEmpty())
        return Result.paramError("用户名不能为空"); 
      if (password == null || password.trim().isEmpty())
        return Result.paramError("密码不能为空"); 
      if (captchaId == null || captchaId.trim().isEmpty())
        return Result.paramError("验证码ID不能为空"); 
      if (captchaCode == null || captchaCode.trim().isEmpty())
        return Result.paramError("验证码不能为空"); 
      if (!this.captchaService.verifyCaptcha(captchaId, captchaCode))
        return Result.error("验证码错误或已过期"); 
      AdminUser adminUser = this.adminService.login(username, password);
      String token = this.adminService.generateToken(adminUser);
      String ipAddress = getClientIpAddress(request);
      this.adminService.updateLastLogin(adminUser.getId(), ipAddress);
      this.adminService.logAction(adminUser.getId(), adminUser.getUsername(), "LOGIN", "ADMIN", adminUser
          .getId().toString(), "管理员登录", ipAddress, request
          .getHeader("User-Agent"));
      Map<String, Object> result = new HashMap<>();
      result.put("token", token);
      result.put("user", adminUser);
      return Result.success("登录成功", result);
    } catch (Exception e) {
      log.error("管理员登录失败", e);
      return Result.error("登录失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/verify"})
  public Result<AdminUser> verifyToken(HttpServletRequest request) {
    try {
      String token = getTokenFromRequest(request);
      if (token == null)
        return Result.error("Token不能为空"); 
      AdminUser adminUser = this.adminService.verifyToken(token);
      return Result.success("Token验证成功", adminUser);
    } catch (Exception e) {
      log.error("Token验证失败", e);
      return Result.error("Token验证失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/logout"})
  public Result<String> logout(HttpServletRequest request) {
    try {
      String token = getTokenFromRequest(request);
      if (token != null) {
        AdminUser adminUser = this.adminService.verifyToken(token);
        String ipAddress = getClientIpAddress(request);
        this.adminService.logAction(adminUser.getId(), adminUser.getUsername(), "LOGOUT", "ADMIN", adminUser
            .getId().toString(), "管理员登出", ipAddress, request
            .getHeader("User-Agent"));
      } 
      return Result.success("登出成功");
    } catch (Exception e) {
      log.error("管理员登出失败", e);
      return Result.success("登出成功");
    } 
  }
  
  @GetMapping({"/stats"})
  public Result<Map<String, Object>> getStats() {
    try {
      Map<String, Object> stats = new HashMap<>();
      long userCount = this.userMapper.selectCount(null).longValue();
      stats.put("userCount", Long.valueOf(userCount));
      long flowerCount = this.flowerMapper.selectCount(null).longValue();
      stats.put("flowerCount", Long.valueOf(flowerCount));
      long orderCount = this.orderMapper.selectCount(null).longValue();
      stats.put("orderCount", Long.valueOf(orderCount));
      BigDecimal totalSales = BigDecimal.ZERO;
      List<Order> orders = this.orderMapper.selectList(null);
      for (Order order : orders) {
        if (order.getTotalAmount() != null)
          totalSales = totalSales.add(order.getTotalAmount()); 
      } 
      stats.put("totalSales", totalSales);
      return Result.success(stats);
    } catch (Exception e) {
      log.error("获取统计数据失败", e);
      return Result.error("获取统计数据失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/chart/{type}"})
  public Result<Map<String, Object>> getChartData(@PathVariable String type) {
    try {
      int days;
      List<String> dates;
      List<Integer> counts;
      LocalDateTime endDate, startDate;
      int i;
      List<Category> categories;
      List<Map<String, Object>> categoryData;
      List<String> salesDates;
      List<BigDecimal> salesAmounts;
      LocalDateTime salesEndDate, salesStartDate;
      int j;
      List<String> userDates;
      List<Integer> userCounts;
      LocalDateTime userEndDate, userStartDate;
      int k;
      List<Map<String, Object>> stockData;
      LambdaQueryWrapper<Flower> stockWrapper1;
      long outOfStock;
      LambdaQueryWrapper<Flower> stockWrapper2;
      long lowStock;
      LambdaQueryWrapper<Flower> stockWrapper3;
      long normalStock;
      LambdaQueryWrapper<Flower> stockWrapper4;
      long highStock;
      List<Map<String, Object>> orderStatusData;
      String[] statusNames;
      int status;
      LambdaQueryWrapper<Flower> hotWrapper;
      List<Flower> hotFlowers;
      List<Map<String, Object>> hotProductsData;
      Map<String, Object> chartData = new HashMap<>();
      switch (type) {
        case "order-week":
        case "order-month":
          days = type.equals("order-week") ? 7 : 30;
          dates = new ArrayList<>();
          counts = new ArrayList<>();
          endDate = LocalDateTime.now();
          startDate = endDate.minusDays((days - 1));
          for (i = 0; i < days; i++) {
            LocalDateTime currentDate = startDate.plusDays(i);
            String dateStr = currentDate.format(DateTimeFormatter.ofPattern("MM-dd"));
            dates.add(dateStr);
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper();
            wrapper.ge(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay());
            wrapper.lt(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay().plusDays(1L));
            long dayCount = this.orderMapper.selectCount((Wrapper)wrapper).longValue();
            counts.add(Integer.valueOf((int)dayCount));
          } 
          chartData.put("dates", dates);
          chartData.put("counts", counts);
          return Result.success(chartData);
        case "category-sales":
          categories = this.categoryMapper.selectList(null);
          categoryData = new ArrayList<>();
          for (Category category : categories) {
            LambdaQueryWrapper<Flower> flowerWrapper = new LambdaQueryWrapper();
            flowerWrapper.eq(Flower::getCategoryId, category.getId());
            List<Flower> flowers = this.flowerMapper.selectList((Wrapper)flowerWrapper);
            int totalSales = 0;
            for (Flower flower : flowers) {
              if (flower.getSalesCount() != null)
                totalSales += flower.getSalesCount().intValue(); 
            } 
            if (totalSales > 0) {
              Map<String, Object> item = new HashMap<>();
              item.put("name", category.getName());
              item.put("value", Integer.valueOf(totalSales));
              categoryData.add(item);
            } 
          } 
          chartData.put("categories", categoryData);
          return Result.success(chartData);
        case "sales-trend":
          salesDates = new ArrayList<>();
          salesAmounts = new ArrayList<>();
          salesEndDate = LocalDateTime.now();
          salesStartDate = salesEndDate.minusDays(29L);
          for (j = 0; j < 30; j++) {
            LocalDateTime currentDate = salesStartDate.plusDays(j);
            String dateStr = currentDate.format(DateTimeFormatter.ofPattern("MM-dd"));
            salesDates.add(dateStr);
            LambdaQueryWrapper<Order> salesWrapper = new LambdaQueryWrapper();
            salesWrapper.ge(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay());
            salesWrapper.lt(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay().plusDays(1L));
            salesWrapper.in(Order::getStatus, Arrays.asList(new Integer[] { Integer.valueOf(2), Integer.valueOf(3), Integer.valueOf(4) }));
            List<Order> dayOrders = this.orderMapper.selectList((Wrapper)salesWrapper);
            BigDecimal dayAmount = BigDecimal.ZERO;
            for (Order order : dayOrders) {
              if (order.getTotalAmount() != null)
                dayAmount = dayAmount.add(order.getTotalAmount()); 
            } 
            salesAmounts.add(dayAmount);
          } 
          chartData.put("dates", salesDates);
          chartData.put("amounts", salesAmounts);
          return Result.success(chartData);
        case "user-growth":
          userDates = new ArrayList<>();
          userCounts = new ArrayList<>();
          userEndDate = LocalDateTime.now();
          userStartDate = userEndDate.minusDays(29L);
          for (k = 0; k < 30; k++) {
            LocalDateTime currentDate = userStartDate.plusDays(k);
            String dateStr = currentDate.format(DateTimeFormatter.ofPattern("MM-dd"));
            userDates.add(dateStr);
            LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper();
            userWrapper.le(User::getCreatedAt, currentDate.toLocalDate().atStartOfDay().plusDays(1L));
            long totalUsers = this.userMapper.selectCount((Wrapper)userWrapper).longValue();
            userCounts.add(Integer.valueOf((int)totalUsers));
          } 
          chartData.put("dates", userDates);
          chartData.put("counts", userCounts);
          return Result.success(chartData);
        case "stock-status":
          stockData = new ArrayList<>();
          stockWrapper1 = new LambdaQueryWrapper();
          stockWrapper1.eq(Flower::getStockQuantity, Integer.valueOf(0));
          outOfStock = this.flowerMapper.selectCount((Wrapper)stockWrapper1).longValue();
          if (outOfStock > 0L) {
            Map<String, Object> item1 = new HashMap<>();
            item1.put("name", "缺货");
            item1.put("value", Long.valueOf(outOfStock));
            stockData.add(item1);
          } 
          stockWrapper2 = new LambdaQueryWrapper();
          stockWrapper2.gt(Flower::getStockQuantity, Integer.valueOf(0));
          stockWrapper2.le(Flower::getStockQuantity, Integer.valueOf(10));
          lowStock = this.flowerMapper.selectCount((Wrapper)stockWrapper2).longValue();
          if (lowStock > 0L) {
            Map<String, Object> item2 = new HashMap<>();
            item2.put("name", "库存不足");
            item2.put("value", Long.valueOf(lowStock));
            stockData.add(item2);
          } 
          stockWrapper3 = new LambdaQueryWrapper();
          stockWrapper3.gt(Flower::getStockQuantity, Integer.valueOf(10));
          stockWrapper3.le(Flower::getStockQuantity, Integer.valueOf(50));
          normalStock = this.flowerMapper.selectCount((Wrapper)stockWrapper3).longValue();
          if (normalStock > 0L) {
            Map<String, Object> item3 = new HashMap<>();
            item3.put("name", "库存正常");
            item3.put("value", Long.valueOf(normalStock));
            stockData.add(item3);
          } 
          stockWrapper4 = new LambdaQueryWrapper();
          stockWrapper4.gt(Flower::getStockQuantity, Integer.valueOf(50));
          highStock = this.flowerMapper.selectCount((Wrapper)stockWrapper4).longValue();
          if (highStock > 0L) {
            Map<String, Object> item4 = new HashMap<>();
            item4.put("name", "库存充足");
            item4.put("value", Long.valueOf(highStock));
            stockData.add(item4);
          } 
          chartData.put("stockStatus", stockData);
          return Result.success(chartData);
        case "order-status":
          orderStatusData = new ArrayList<>();
          statusNames = new String[] { "待付款", "已付款", "已发货", "已完成", "已取消" };
          for (status = 1; status <= 5; status++) {
            LambdaQueryWrapper<Order> statusWrapper = new LambdaQueryWrapper();
            statusWrapper.eq(Order::getStatus, Integer.valueOf(status));
            long statusCount = this.orderMapper.selectCount((Wrapper)statusWrapper).longValue();
            if (statusCount > 0L) {
              Map<String, Object> item = new HashMap<>();
              item.put("name", statusNames[status - 1]);
              item.put("value", Long.valueOf(statusCount));
              orderStatusData.add(item);
            } 
          } 
          chartData.put("orderStatus", orderStatusData);
          return Result.success(chartData);
        case "hot-products":
          hotWrapper = new LambdaQueryWrapper();
          hotWrapper.orderByDesc(Flower::getSalesCount);
          hotWrapper.last("LIMIT 10");
          hotFlowers = this.flowerMapper.selectList((Wrapper)hotWrapper);
          hotProductsData = new ArrayList<>();
          for (Flower flower : hotFlowers) {
            if (flower.getSalesCount() != null && flower.getSalesCount().intValue() > 0) {
              Map<String, Object> item = new HashMap<>();
              item.put("name", flower.getName());
              item.put("sales", flower.getSalesCount());
              item.put("price", flower.getPrice());
              hotProductsData.add(item);
            } 
          } 
          chartData.put("hotProducts", hotProductsData);
          return Result.success(chartData);
      } 
      return Result.error("不支持的图表类型");
    } catch (Exception e) {
      log.error("获取图表数据失败", e);
      return Result.error("获取图表数据失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/users"})
  public Result<PageResult<User>> getUsers(@RequestParam(defaultValue = "1") Long current, @RequestParam(defaultValue = "10") Long size, @RequestParam(required = false) String keyword, @RequestParam(required = false) Integer status) {
    try {
      Page<User> page = new Page<>(current.longValue(), size.longValue());
      LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
      if (keyword != null && !keyword.trim().isEmpty())
        wrapper.and(w -> w.like(User::getNickname, keyword).or().like(User::getPhone, keyword));
      if (status != null)
        wrapper.eq(User::getStatus, status); 
      wrapper.orderByDesc(User::getCreatedAt);
      Page<User> result = (Page<User>)this.userMapper.selectPage((IPage)page, (Wrapper)wrapper);
      PageResult<User> pageResult = new PageResult();
      pageResult.setRecords(result.getRecords());
      pageResult.setTotal(Long.valueOf(result.getTotal()));
      pageResult.setCurrent(Long.valueOf(result.getCurrent()));
      pageResult.setSize(Long.valueOf(result.getSize()));
      pageResult.setPages(Long.valueOf(result.getPages()));
      return Result.success(pageResult);
    } catch (Exception e) {
      log.error("获取用户列表失败", e);
      return Result.error("获取用户列表失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/users/{id}"})
  public Result<User> getUserDetail(@PathVariable Long id) {
    try {
      User user = (User)this.userMapper.selectById(id);
      if (user == null)
        return Result.notFound("用户不存在"); 
      return Result.success(user);
    } catch (Exception e) {
      log.error("获取用户详情失败", e);
      return Result.error("获取用户详情失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/users/{id}"})
  public Result<User> updateUser(@PathVariable Long id, @RequestBody User user) {
    try {
      User existingUser = (User)this.userMapper.selectById(id);
      if (existingUser == null)
        return Result.notFound("用户不存在"); 
      user.setId(id);
      user.setUpdatedAt(LocalDateTime.now());
      user.setCreatedAt(existingUser.getCreatedAt());
      user.setOpenid(existingUser.getOpenid());
      user.setUnionid(existingUser.getUnionid());
      user.setSessionKey(existingUser.getSessionKey());
      int result = this.userMapper.updateById(user);
      if (result > 0) {
        User updatedUser = (User)this.userMapper.selectById(id);
        return Result.success("用户信息更新成功", updatedUser);
      } 
      return Result.error("更新用户信息失败");
    } catch (Exception e) {
      log.error("更新用户信息失败", e);
      return Result.error("更新用户信息失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/test-cors"})
  public Result<String> testCors() {
    return Result.success("CORS配置正常");
  }
  
  @GetMapping({"/admin-users"})
  public Result<PageResult<AdminUser>> getAdminUsers(@RequestParam(defaultValue = "1") Long current, @RequestParam(defaultValue = "10") Long size, @RequestParam(required = false) String keyword, @RequestParam(required = false) Integer status, @RequestParam(required = false) String role) {
    try {
      Page<AdminUser> page = new Page<>(current.longValue(), size.longValue());
      LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();
      if (keyword != null && !keyword.trim().isEmpty())
        wrapper.and(w -> w.like(AdminUser::getUsername, keyword).or().like(AdminUser::getEmail, keyword).or().like(AdminUser::getRealName, keyword));
      if (status != null)
        wrapper.eq(AdminUser::getStatus, status); 
      if (role != null && !role.trim().isEmpty())
        wrapper.eq(AdminUser::getRole, role); 
      wrapper.orderByAsc(AdminUser::getId);
      Page<AdminUser> result = (Page<AdminUser>)this.adminUserMapper.selectPage((IPage)page, (Wrapper)wrapper);
      PageResult<AdminUser> pageResult = new PageResult();
      pageResult.setRecords(result.getRecords());
      pageResult.setTotal(Long.valueOf(result.getTotal()));
      pageResult.setCurrent(Long.valueOf(result.getCurrent()));
      pageResult.setSize(Long.valueOf(result.getSize()));
      pageResult.setPages(Long.valueOf(result.getPages()));
      return Result.success(pageResult);
    } catch (Exception e) {
      log.error("获取后端用户列表失败", e);
      return Result.error("获取后端用户列表失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/admin-users/{id}"})
  public Result<AdminUser> getAdminUserDetail(@PathVariable Long id) {
    try {
      AdminUser adminUser = (AdminUser)this.adminUserMapper.selectById(id);
      if (adminUser == null)
        return Result.notFound("用户不存在"); 
      return Result.success(adminUser);
    } catch (Exception e) {
      log.error("获取后端用户详情失败", e);
      return Result.error("获取后端用户详情失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/admin-users"})
  public Result<AdminUser> createAdminUser(@RequestBody AdminUser adminUser) {
    try {
      if (adminUser.getUsername() == null || adminUser.getUsername().trim().isEmpty())
        return Result.paramError("用户名不能为空"); 
      if (adminUser.getEmail() == null || adminUser.getEmail().trim().isEmpty())
        return Result.paramError("邮箱不能为空"); 
      if (adminUser.getPassword() == null || adminUser.getPassword().trim().isEmpty())
        return Result.paramError("密码不能为空"); 
      if (adminUser.getRealName() == null || adminUser.getRealName().trim().isEmpty())
        return Result.paramError("真实姓名不能为空"); 
      if (adminUser.getRole() == null || adminUser.getRole().trim().isEmpty())
        return Result.paramError("角色不能为空"); 
      LambdaQueryWrapper<AdminUser> usernameWrapper = new LambdaQueryWrapper();
      usernameWrapper.eq(AdminUser::getUsername, adminUser.getUsername().trim());
      AdminUser existingByUsername = (AdminUser)this.adminUserMapper.selectOne((Wrapper)usernameWrapper);
      if (existingByUsername != null)
        return Result.paramError("用户名已存在"); 
      LambdaQueryWrapper<AdminUser> emailWrapper = new LambdaQueryWrapper();
      emailWrapper.eq(AdminUser::getEmail, adminUser.getEmail().trim());
      AdminUser existingByEmail = (AdminUser)this.adminUserMapper.selectOne((Wrapper)emailWrapper);
      if (existingByEmail != null)
        return Result.paramError("邮箱已存在"); 
      adminUser.setUsername(adminUser.getUsername().trim());
      adminUser.setEmail(adminUser.getEmail().trim());
      adminUser.setRealName(adminUser.getRealName().trim());
      adminUser.setRole(adminUser.getRole().trim());
      adminUser.setPassword(PasswordUtil.encode(adminUser.getPassword()));
      if (adminUser.getStatus() == null)
        adminUser.setStatus(Integer.valueOf(1)); 
      adminUser.setCreatedAt(LocalDateTime.now());
      adminUser.setUpdatedAt(LocalDateTime.now());
      this.adminUserMapper.insert(adminUser);
      adminUser.setPassword(null);
      return Result.success("后端用户创建成功", adminUser);
    } catch (Exception e) {
      log.error("创建后端用户失败", e);
      return Result.error("创建后端用户失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/admin-users/{id}"})
  public Result<AdminUser> updateAdminUser(@PathVariable Long id, @RequestBody AdminUser adminUser) {
    try {
      AdminUser existing = (AdminUser)this.adminUserMapper.selectById(id);
      if (existing == null)
        return Result.notFound("用户不存在"); 
      if (adminUser.getUsername() == null || adminUser.getUsername().trim().isEmpty())
        return Result.paramError("用户名不能为空"); 
      if (adminUser.getEmail() == null || adminUser.getEmail().trim().isEmpty())
        return Result.paramError("邮箱不能为空"); 
      if (adminUser.getRealName() == null || adminUser.getRealName().trim().isEmpty())
        return Result.paramError("真实姓名不能为空"); 
      if (adminUser.getRole() == null || adminUser.getRole().trim().isEmpty())
        return Result.paramError("角色不能为空"); 
      LambdaQueryWrapper<AdminUser> usernameWrapper = new LambdaQueryWrapper<>();
      usernameWrapper.eq(AdminUser::getUsername, adminUser.getUsername().trim())
        .ne(AdminUser::getId, id);
      AdminUser duplicateUsername = (AdminUser)this.adminUserMapper.selectOne((Wrapper)usernameWrapper);
      if (duplicateUsername != null)
        return Result.paramError("用户名已存在"); 
      LambdaQueryWrapper<AdminUser> emailWrapper = new LambdaQueryWrapper<>();
      emailWrapper.eq(AdminUser::getEmail, adminUser.getEmail().trim())
        .ne(AdminUser::getId, id);
      AdminUser duplicateEmail = (AdminUser)this.adminUserMapper.selectOne((Wrapper)emailWrapper);
      if (duplicateEmail != null)
        return Result.paramError("邮箱已存在"); 
      existing.setUsername(adminUser.getUsername().trim());
      existing.setEmail(adminUser.getEmail().trim());
      existing.setRealName(adminUser.getRealName().trim());
      existing.setRole(adminUser.getRole().trim());
      if (adminUser.getPhone() != null)
        existing.setPhone(adminUser.getPhone().trim()); 
      if (adminUser.getAvatar() != null)
        existing.setAvatar(adminUser.getAvatar()); 
      if (adminUser.getRemark() != null)
        existing.setRemark(adminUser.getRemark()); 
      if (adminUser.getStatus() != null)
        existing.setStatus(adminUser.getStatus()); 
      if (adminUser.getPassword() != null && !adminUser.getPassword().trim().isEmpty())
        existing.setPassword(PasswordUtil.encode(adminUser.getPassword())); 
      existing.setUpdatedAt(LocalDateTime.now());
      this.adminUserMapper.updateById(existing);
      existing.setPassword(null);
      return Result.success("后端用户更新成功", existing);
    } catch (Exception e) {
      log.error("更新后端用户失败", e);
      return Result.error("更新后端用户失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/admin-users/{id}/status"})
  public Result<String> updateAdminUserStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
    try {
      Integer status = params.get("status");
      if (status == null)
        return Result.paramError("状态参数不能为空"); 
      AdminUser adminUser = (AdminUser)this.adminUserMapper.selectById(id);
      if (adminUser == null)
        return Result.notFound("用户不存在"); 
      adminUser.setStatus(status);
      adminUser.setUpdatedAt(LocalDateTime.now());
      this.adminUserMapper.updateById(adminUser);
      String statusText = (status.intValue() == 1) ? "启用" : "禁用";
      return Result.success("用户状态已更新为" + statusText);
    } catch (Exception e) {
      log.error("更新后端用户状态失败", e);
      return Result.error("更新后端用户状态失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/admin-users/{id}"})
  public Result<String> deleteAdminUser(@PathVariable Long id) {
    try {
      AdminUser adminUser = (AdminUser)this.adminUserMapper.selectById(id);
      if (adminUser == null)
        return Result.notFound("用户不存在"); 
      if ("super_admin".equals(adminUser.getRole()))
        return Result.error("不能删除超级管理员"); 
      this.adminUserMapper.deleteById(id);
      return Result.success("用户删除成功");
    } catch (Exception e) {
      log.error("删除后端用户失败", e);
      return Result.error("删除后端用户失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/admin-users/batch"})
  public Result<String> batchDeleteAdminUsers(@RequestBody Map<String, List<Long>> params) {
    try {
      List<Long> ids = params.get("ids");
      if (ids == null || ids.isEmpty())
        return Result.paramError("用户ID列表不能为空"); 
      LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper();
      wrapper.in(AdminUser::getId, ids);
      wrapper.eq(AdminUser::getRole, "super_admin");
      Long superAdminCount = this.adminUserMapper.selectCount((Wrapper)wrapper);
      if (superAdminCount.longValue() > 0L)
        return Result.error("不能删除超级管理员"); 
      int deletedCount = this.adminUserMapper.deleteBatchIds(ids);
      return Result.success("成功删除 " + deletedCount + " 个用户");
    } catch (Exception e) {
      log.error("批量删除后端用户失败", e);
      return Result.error("批量删除后端用户失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/admin-users/batch/status"})
  public Result<String> batchUpdateAdminUserStatus(@RequestBody Map<String, Object> params) {
    try {
      List<Long> ids = (List<Long>)params.get("ids");
      Integer status = (Integer)params.get("status");
      if (ids == null || ids.isEmpty())
        return Result.paramError("用户ID列表不能为空"); 
      if (status == null)
        return Result.paramError("状态参数不能为空"); 
      for (Long id : ids) {
        AdminUser adminUser = (AdminUser)this.adminUserMapper.selectById(id);
        if (adminUser != null) {
          adminUser.setStatus(status);
          adminUser.setUpdatedAt(LocalDateTime.now());
          this.adminUserMapper.updateById(adminUser);
        } 
      } 
      String statusText = (status.intValue() == 1) ? "启用" : "禁用";
      return Result.success("成功" + statusText + " " + ids.size() + " 个用户");
    } catch (Exception e) {
      log.error("批量更新后端用户状态失败", e);
      return Result.error("批量更新后端用户状态失败: " + e.getMessage());
    } 
  }
  
  private String getClientIpAddress(HttpServletRequest request) {
    String xForwardedFor = request.getHeader("X-Forwarded-For");
    if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor))
      return xForwardedFor.split(",")[0]; 
    String xRealIp = request.getHeader("X-Real-IP");
    if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp))
      return xRealIp; 
    return request.getRemoteAddr();
  }
  
  private String getTokenFromRequest(HttpServletRequest request) {
    String authHeader = request.getHeader("Authorization");
    if (authHeader != null && authHeader.startsWith("Bearer "))
      return authHeader.substring(7); 
    return null;
  }
  
  @GetMapping({"/orders"})
  public Result<PageResult<Map<String, Object>>> getOrders(@RequestParam(defaultValue = "1") Integer current, @RequestParam(defaultValue = "10") Integer size, @RequestParam(required = false) String keyword, @RequestParam(required = false) Integer status, @RequestParam(defaultValue = "created_at") String sortBy, @RequestParam(defaultValue = "desc") String sortOrder) {
    try {
      Page<Order> page = new Page<>(current.intValue(), size.intValue());
      QueryWrapper<Order> wrapper = new QueryWrapper();
      if (keyword != null && !keyword.trim().isEmpty())
        ((QueryWrapper)((QueryWrapper)((QueryWrapper)((QueryWrapper)wrapper.like("recipient_name", keyword))
          .or())
          .like("recipient_phone", keyword))
          .or())
          .like("order_no", keyword); 
      if (status != null)
        wrapper.eq("status", status); 
      if ("asc".equalsIgnoreCase(sortOrder)) {
        wrapper.orderByAsc(sortBy);
      } else {
        wrapper.orderByDesc(sortBy);
      } 
      Page<Order> result = (Page<Order>)this.orderMapper.selectPage((IPage)page, (Wrapper)wrapper);
      List<Map<String, Object>> enrichedOrders = new ArrayList<>();
      for (Order order : result.getRecords()) {
        Map<String, Object> orderMap = new HashMap<>();
        orderMap.put("id", order.getId());
        orderMap.put("orderNo", order.getOrderNo());
        orderMap.put("userId", order.getUserId());
        orderMap.put("totalAmount", order.getTotalAmount());
        orderMap.put("discountAmount", order.getDiscountAmount());
        orderMap.put("finalAmount", order.getFinalAmount());
        orderMap.put("status", order.getStatus());
        orderMap.put("paymentMethod", order.getPaymentMethod());
        orderMap.put("paymentStatus", order.getPaymentStatus());
        orderMap.put("recipientName", order.getRecipientName());
        orderMap.put("recipientPhone", order.getRecipientPhone());
        orderMap.put("recipientAddress", order.getRecipientAddress());
        orderMap.put("deliveryNotes", order.getDeliveryNotes());
        orderMap.put("remark", order.getRemark());
        orderMap.put("deliveryType", order.getDeliveryType());
        orderMap.put("pickupName", order.getPickupName());
        orderMap.put("pickupPhone", order.getPickupPhone());
        orderMap.put("pickupTime", order.getPickupTime());
        orderMap.put("deliveryTime", order.getDeliveryTime());
        orderMap.put("paidAt", order.getPaidAt());
        orderMap.put("shippedAt", order.getShippedAt());
        orderMap.put("deliveredAt", order.getDeliveredAt());
        orderMap.put("createdAt", order.getCreatedAt());
        orderMap.put("updatedAt", order.getUpdatedAt());
        User user = (User)this.userMapper.selectById(order.getUserId());
        if (user != null) {
          orderMap.put("userName", user.getNickname());
          orderMap.put("userPhone", user.getPhone());
          orderMap.put("userAvatar", user.getAvatarUrl());
        } else {
          orderMap.put("userName", "未知用户");
          orderMap.put("userPhone", "");
          orderMap.put("userAvatar", "");
        } 
        enrichedOrders.add(orderMap);
      } 
      return Result.success(new PageResult(enrichedOrders, 
            
            Long.valueOf(result.getTotal()), 
            Long.valueOf(result.getCurrent()), 
            Long.valueOf(result.getSize())));
    } catch (Exception e) {
      return Result.error("获取订单列表失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/orders/{id}"})
  public Result<Map<String, Object>> getOrderDetail(@PathVariable Long id) {
    try {
      Order order = (Order)this.orderMapper.selectById(id);
      if (order == null)
        return Result.notFound("订单不存在"); 
      Map<String, Object> orderDetail = new HashMap<>();
      orderDetail.put("id", order.getId());
      orderDetail.put("orderNo", order.getOrderNo());
      orderDetail.put("userId", order.getUserId());
      orderDetail.put("totalAmount", order.getTotalAmount());
      orderDetail.put("discountAmount", order.getDiscountAmount());
      orderDetail.put("finalAmount", order.getFinalAmount());
      orderDetail.put("status", order.getStatus());
      orderDetail.put("paymentMethod", order.getPaymentMethod());
      orderDetail.put("paymentStatus", order.getPaymentStatus());
      orderDetail.put("recipientName", order.getRecipientName());
      orderDetail.put("recipientPhone", order.getRecipientPhone());
      orderDetail.put("recipientAddress", order.getRecipientAddress());
      orderDetail.put("deliveryNotes", order.getDeliveryNotes());
      orderDetail.put("remark", order.getRemark());
      orderDetail.put("deliveryType", order.getDeliveryType());
      orderDetail.put("pickupName", order.getPickupName());
      orderDetail.put("pickupPhone", order.getPickupPhone());
      orderDetail.put("pickupTime", order.getPickupTime());
      orderDetail.put("deliveryTime", order.getDeliveryTime());
      orderDetail.put("paidAt", order.getPaidAt());
      orderDetail.put("shippedAt", order.getShippedAt());
      orderDetail.put("deliveredAt", order.getDeliveredAt());
      orderDetail.put("createdAt", order.getCreatedAt());
      orderDetail.put("updatedAt", order.getUpdatedAt());
      User user = (User)this.userMapper.selectById(order.getUserId());
      if (user != null) {
        orderDetail.put("userName", user.getNickname());
        orderDetail.put("userPhone", user.getPhone());
        orderDetail.put("userAvatar", user.getAvatarUrl());
      } else {
        orderDetail.put("userName", "未知用户");
        orderDetail.put("userPhone", "");
        orderDetail.put("userAvatar", "");
      } 
      LambdaQueryWrapper<OrderItem> itemWrapper = new LambdaQueryWrapper();
      itemWrapper.eq(OrderItem::getOrderId, id);
      List<OrderItem> orderItems = this.orderItemMapper.selectList((Wrapper)itemWrapper);
      orderDetail.put("items", orderItems);
      return Result.success(orderDetail);
    } catch (Exception e) {
      log.error("获取订单详情失败", e);
      return Result.error("获取订单详情失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/orders/{id}/status"})
  public Result<String> updateOrderStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
    try {
      Integer status = params.get("status");
      if (status == null)
        return Result.paramError("状态参数不能为空"); 
      Order order = (Order)this.orderMapper.selectById(id);
      if (order == null)
        return Result.notFound("订单不存在"); 
      if (!isValidStatusTransition(order.getStatus(), order.getPaymentStatus(), status))
        return Result.error("无效的状态转换"); 
      this.orderService.updateOrderStatus(id, status);
      String statusText = getOrderStatusText(status);
      return Result.success("订单状态已更新为" + statusText);
    } catch (Exception e) {
      log.error("更新订单状态失败", e);
      return Result.error("更新订单状态失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/orders/{id}"})
  public Result<String> updateOrder(@PathVariable Long id, @RequestBody Map<String, Object> params) {
    try {
      Order order = (Order)this.orderMapper.selectById(id);
      if (order == null)
        return Result.notFound("订单不存在"); 
      if (params.containsKey("recipientName"))
        order.setRecipientName((String)params.get("recipientName")); 
      if (params.containsKey("recipientPhone"))
        order.setRecipientPhone((String)params.get("recipientPhone")); 
      if (params.containsKey("recipientAddress"))
        order.setRecipientAddress((String)params.get("recipientAddress")); 
      if (params.containsKey("deliveryType"))
        order.setDeliveryType((Integer)params.get("deliveryType")); 
      if (params.containsKey("deliveryNotes"))
        order.setDeliveryNotes((String)params.get("deliveryNotes")); 
      if (params.containsKey("remark"))
        order.setRemark((String)params.get("remark")); 
      if (params.containsKey("status")) {
        Integer newStatus = (Integer)params.get("status");
        order.setStatus(newStatus);
        if (newStatus.intValue() == 3) {
          order.setShippedAt(LocalDateTime.now());
          order.setDeliveryStatus(Integer.valueOf(1));
        } else if (newStatus.intValue() == 1) {
          order.setDeliveredAt(LocalDateTime.now());
          order.setDeliveryStatus(Integer.valueOf(2));
        } else if (newStatus.intValue() == 4) {
          order.setDeliveryStatus(Integer.valueOf(3));
        } 
      } 
      if (params.containsKey("paymentStatus")) {
        Integer paymentStatus = (Integer)params.get("paymentStatus");
        order.setPaymentStatus(paymentStatus);
        if (paymentStatus.intValue() == 1)
          order.setPaidAt(LocalDateTime.now()); 
      } 
      if (params.containsKey("paymentMethod"))
        order.setPaymentMethod((String)params.get("paymentMethod")); 
      boolean amountChanged = false;
      if (params.containsKey("totalAmount")) {
        Object totalAmountObj = params.get("totalAmount");
        if (totalAmountObj instanceof Number) {
          order.setTotalAmount(new BigDecimal(totalAmountObj.toString()));
          amountChanged = true;
        } 
      } 
      if (params.containsKey("discountAmount")) {
        Object discountAmountObj = params.get("discountAmount");
        if (discountAmountObj instanceof Number) {
          order.setDiscountAmount(new BigDecimal(discountAmountObj.toString()));
          amountChanged = true;
        } 
      } 
      if (params.containsKey("finalAmount")) {
        Object finalAmountObj = params.get("finalAmount");
        if (finalAmountObj instanceof Number)
          order.setFinalAmount(new BigDecimal(finalAmountObj.toString())); 
      } else if (amountChanged) {
        BigDecimal totalAmount = (order.getTotalAmount() != null) ? order.getTotalAmount() : BigDecimal.ZERO;
        BigDecimal discountAmount = (order.getDiscountAmount() != null) ? order.getDiscountAmount() : BigDecimal.ZERO;
        BigDecimal finalAmount = totalAmount.subtract(discountAmount);
        order.setFinalAmount((finalAmount.compareTo(BigDecimal.ZERO) < 0) ? BigDecimal.ZERO : finalAmount);
      } 
      order.setUpdatedAt(LocalDateTime.now());
      this.orderMapper.updateById(order);
      return Result.success("订单修改成功");
    } catch (Exception e) {
      log.error("修改订单失败", e);
      return Result.error("修改订单失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/orders/{id}/confirm-payment"})
  public Result<String> confirmPayment(@PathVariable Long id) {
    try {
      Order order = (Order)this.orderMapper.selectById(id);
      if (order == null)
        return Result.notFound("订单不存在"); 
      if (order.getStatus().intValue() != 1)
        return Result.error("只有待付款状态的订单才能确认付款"); 
      if (order.getPaymentStatus().intValue() == 1)
        return Result.error("订单已确认付款"); 
      this.orderService.confirmPayment(id);
      return Result.success("付款确认成功，等待用户确认收货");
    } catch (Exception e) {
      log.error("确认付款失败", e);
      return Result.error("确认付款失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/orders/{id}"})
  public Result<String> deleteOrder(@PathVariable Long id) {
    try {
      Order order = (Order)this.orderMapper.selectById(id);
      if (order == null)
        return Result.notFound("订单不存在"); 
      LambdaQueryWrapper<OrderItem> itemWrapper = new LambdaQueryWrapper();
      itemWrapper.eq(OrderItem::getOrderId, id);
      this.orderItemMapper.delete((Wrapper)itemWrapper);
      int deleted = this.orderMapper.deleteById(id);
      if (deleted > 0)
        return Result.success("订单删除成功"); 
      return Result.error("订单删除失败");
    } catch (Exception e) {
      log.error("删除订单失败", e);
      return Result.error("删除订单失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/orders/batch"})
  public Result<String> batchDeleteOrders(@RequestBody Map<String, List<Long>> params) {
    try {
      List<Long> ids = params.get("ids");
      if (ids == null || ids.isEmpty())
        return Result.paramError("订单ID列表不能为空"); 
      int successCount = 0;
      for (Long id : ids) {
        Order order = (Order)this.orderMapper.selectById(id);
        if (order == null)
          continue; 
        LambdaQueryWrapper<OrderItem> itemWrapper = new LambdaQueryWrapper();
        itemWrapper.eq(OrderItem::getOrderId, id);
        this.orderItemMapper.delete((Wrapper)itemWrapper);
        int deleted = this.orderMapper.deleteById(id);
        if (deleted > 0)
          successCount++; 
      } 
      return Result.success("成功删除 " + successCount + " 个订单");
    } catch (Exception e) {
      log.error("批量删除订单失败", e);
      return Result.error("批量删除订单失败: " + e.getMessage());
    } 
  }
  
  private boolean isValidStatusTransition(Integer currentStatus, Integer paymentStatus, Integer newStatus) {
    if (currentStatus.intValue() == 5)
      return false; 
    if (currentStatus.intValue() == 4 && newStatus.intValue() != 5)
      return false; 
    switch (currentStatus.intValue()) {
      case 2:
        return (newStatus.intValue() == 3 || newStatus.intValue() == 5);
      case 3:
        return (newStatus.intValue() == 1 || newStatus.intValue() == 5);
      case 1:
        return (newStatus.intValue() == 5);
      case 4:
        return (newStatus.intValue() == 5);
    } 
    return false;
  }
  
  private String getOrderStatusText(Integer status) {
    switch (status.intValue()) {
      case 1:
        return "待付款";
      case 2:
        return "已下单";
      case 3:
        return "配送中";
      case 4:
        return "已完成";
      case 5:
        return "已取消";
    } 
    return "未知状态";
  }
  
  @PutMapping({"/users/{id}/status"})
  public Result<String> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
    try {
      Integer status = params.get("status");
      if (status == null)
        return Result.paramError("状态参数不能为空"); 
      User user = (User)this.userMapper.selectById(id);
      if (user == null)
        return Result.notFound("用户不存在"); 
      user.setStatus(status);
      user.setUpdatedAt(LocalDateTime.now());
      this.userMapper.updateById(user);
      String statusText = (status.intValue() == 1) ? "启用" : "禁用";
      return Result.success("用户状态已更新为" + statusText);
    } catch (Exception e) {
      log.error("更新用户状态失败", e);
      return Result.error("更新用户状态失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/users/{id}"})
  public Result<String> deleteUser(@PathVariable Long id) {
    try {
      User user = (User)this.userMapper.selectById(id);
      if (user == null)
        return Result.notFound("用户不存在"); 
      this.userMapper.deleteById(id);
      return Result.success("用户删除成功");
    } catch (Exception e) {
      log.error("删除用户失败", e);
      return Result.error("删除用户失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/users/batch"})
  public Result<String> batchDeleteUsers(@RequestBody Map<String, List<Long>> params) {
    try {
      List<Long> ids = params.get("ids");
      if (ids == null || ids.isEmpty())
        return Result.paramError("用户ID列表不能为空"); 
      int deletedCount = this.userMapper.deleteBatchIds(ids);
      return Result.success("成功删除 " + deletedCount + " 个用户");
    } catch (Exception e) {
      log.error("批量删除用户失败", e);
      return Result.error("批量删除用户失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/categories"})
  public Result<PageResult<Category>> getCategories(@RequestParam(defaultValue = "1") Long current, @RequestParam(defaultValue = "10") Long size, @RequestParam(required = false) String keyword, @RequestParam(required = false) Integer status) {
    try {
      Page<Category> page = new Page<>(current.longValue(), size.longValue());
      LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
      if (keyword != null && !keyword.trim().isEmpty())
        wrapper.and(w -> w.like(Category::getName, keyword).or().like(Category::getDescription, keyword));
      if (status != null)
        wrapper.eq(Category::getStatus, status); 
      wrapper.orderByAsc(Category::getSortOrder);
      Page<Category> result = (Page<Category>)this.categoryMapper.selectPage((IPage)page, (Wrapper)wrapper);
      return Result.success(new PageResult(result
            .getRecords(), 
            Long.valueOf(result.getTotal()), 
            Long.valueOf(result.getCurrent()), 
            Long.valueOf(result.getSize())));
    } catch (Exception e) {
      log.error("获取分类列表失败", e);
      return Result.error("获取分类列表失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/categories"})
  public Result<Category> createCategory(@RequestBody Category category) {
    try {
      if (category.getName() == null || category.getName().trim().isEmpty())
        return Result.paramError("分类名称不能为空"); 
      LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper();
      wrapper.eq(Category::getName, category.getName().trim());
      Category existing = (Category)this.categoryMapper.selectOne((Wrapper)wrapper);
      if (existing != null)
        return Result.paramError("分类名称已存在"); 
      category.setName(category.getName().trim());
      if (category.getStatus() == null)
        category.setStatus(Integer.valueOf(1)); 
      if (category.getSortOrder() == null)
        category.setSortOrder(Integer.valueOf(0)); 
      category.setCreatedAt(LocalDateTime.now());
      category.setUpdatedAt(LocalDateTime.now());
      this.categoryMapper.insert(category);
      return Result.success("分类创建成功", category);
    } catch (Exception e) {
      log.error("创建分类失败", e);
      return Result.error("创建分类失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/categories/{id}"})
  public Result<Category> updateCategory(@PathVariable Long id, @RequestBody Category category) {
    try {
      Category existing = (Category)this.categoryMapper.selectById(id);
      if (existing == null)
        return Result.notFound("分类不存在"); 
      if (category.getName() == null || category.getName().trim().isEmpty())
        return Result.paramError("分类名称不能为空"); 
      LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(Category::getName, category.getName().trim())
        .ne(Category::getId, id);
      Category duplicate = (Category)this.categoryMapper.selectOne((Wrapper)wrapper);
      if (duplicate != null)
        return Result.paramError("分类名称已存在"); 
      existing.setName(category.getName().trim());
      if (category.getDescription() != null)
        existing.setDescription(category.getDescription()); 
      if (category.getImageUrl() != null)
        existing.setImageUrl(category.getImageUrl()); 
      if (category.getStatus() != null)
        existing.setStatus(category.getStatus()); 
      if (category.getSortOrder() != null)
        existing.setSortOrder(category.getSortOrder()); 
      existing.setUpdatedAt(LocalDateTime.now());
      this.categoryMapper.updateById(existing);
      return Result.success("分类更新成功", existing);
    } catch (Exception e) {
      log.error("更新分类失败", e);
      return Result.error("更新分类失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/categories/{id}/status"})
  public Result<String> updateCategoryStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
    try {
      Integer status = params.get("status");
      if (status == null)
        return Result.paramError("状态参数不能为空"); 
      Category category = (Category)this.categoryMapper.selectById(id);
      if (category == null)
        return Result.notFound("分类不存在"); 
      category.setStatus(status);
      category.setUpdatedAt(LocalDateTime.now());
      this.categoryMapper.updateById(category);
      String statusText = (status.intValue() == 1) ? "启用" : "禁用";
      return Result.success("分类已" + statusText);
    } catch (Exception e) {
      log.error("更新分类状态失败", e);
      return Result.error("更新分类状态失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/categories/{id}"})
  public Result<String> deleteCategory(@PathVariable Long id) {
    try {
      Category category = (Category)this.categoryMapper.selectById(id);
      if (category == null)
        return Result.notFound("分类不存在"); 
      LambdaQueryWrapper<Flower> flowerWrapper = new LambdaQueryWrapper();
      flowerWrapper.eq(Flower::getCategoryId, id);
      Long flowerCount = this.flowerMapper.selectCount((Wrapper)flowerWrapper);
      if (flowerCount.longValue() > 0L)
        return Result.error("该分类下还有 " + flowerCount + " 个商品，无法删除"); 
      this.categoryMapper.deleteById(id);
      return Result.success("分类删除成功");
    } catch (Exception e) {
      log.error("删除分类失败", e);
      return Result.error("删除分类失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/flowers"})
  public Result<PageResult<FlowerVO>> getFlowers(@RequestParam(defaultValue = "1") Long current, @RequestParam(defaultValue = "10") Long size, @RequestParam(required = false) String keyword, @RequestParam(required = false) Long categoryId, @RequestParam(required = false) Integer status, @RequestParam(required = false) String categories, @RequestParam(required = false) String statuses, @RequestParam(required = false) String colors, @RequestParam(required = false) String sizes, @RequestParam(required = false) String priceRanges, @RequestParam(required = false) String stockRanges) {
    try {
      Page<Flower> page = new Page<>(current.longValue(), size.longValue());
      LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();
      if (keyword != null && !keyword.trim().isEmpty())
        wrapper.and(w -> w.like(Flower::getName, keyword).or().like(Flower::getDescription, keyword));
      if (categoryId != null)
        wrapper.eq(Flower::getCategoryId, categoryId); 
      if (status != null)
        wrapper.eq(Flower::getStatus, status); 
      if (categories != null && !categories.trim().isEmpty()) {
        String[] categoryArray = categories.split(",");
        List<Long> categoryIds = (List<Long>)Arrays.<String>stream(categoryArray).map(String::trim).filter(s -> !s.isEmpty()).map(s -> {
              try {
                return Long.valueOf(Long.parseLong(s));
              } catch (NumberFormatException e) {
                log.warn("分类ID格式错误: {}", s);
                return null;
              } 
            }).filter(Objects::nonNull).collect(Collectors.toList());
        if (!categoryIds.isEmpty())
          wrapper.in(Flower::getCategoryId, categoryIds); 
      } 
      if (statuses != null && !statuses.trim().isEmpty()) {
        String[] statusArray = statuses.split(",");
        List<Integer> statusList = (List<Integer>)Arrays.<String>stream(statusArray).map(String::trim).filter(s -> !s.isEmpty()).map(s -> {
              try {
                return Integer.valueOf(Integer.parseInt(s));
              } catch (NumberFormatException e) {
                log.warn("状态值格式错误: {}", s);
                return null;
              } 
            }).filter(Objects::nonNull).collect(Collectors.toList());
        if (!statusList.isEmpty())
          wrapper.in(Flower::getStatus, statusList); 
      } 
      if (colors != null && !colors.trim().isEmpty()) {
        String[] colorArray = colors.split(",");
        List<String> validColors = (List<String>)Arrays.<String>stream(colorArray).map(String::trim).filter(c -> !c.isEmpty()).collect(Collectors.toList());
        if (!validColors.isEmpty())
          wrapper.and(w -> {
                for (int i = 0; i < validColors.size(); i++) {
                  if (i == 0) {
                    w.like(Flower::getColor, validColors.get(i));
                  } else {
                    w.or().like(Flower::getColor, validColors.get(i));
                  } 
                } 
              }); 
      } 
      if (sizes != null && !sizes.trim().isEmpty()) {
        String[] sizeArray = sizes.split(",");
        List<String> validSizes = (List<String>)Arrays.<String>stream(sizeArray).map(String::trim).filter(s -> !s.isEmpty()).collect(Collectors.toList());
        if (!validSizes.isEmpty())
          wrapper.and(w -> {
                for (int i = 0; i < validSizes.size(); i++) {
                  if (i == 0) {
                    w.like(Flower::getSize, validSizes.get(i));
                  } else {
                    w.or().like(Flower::getSize, validSizes.get(i));
                  } 
                } 
              }); 
      } 
      if (priceRanges != null && !priceRanges.trim().isEmpty()) {
        String[] ranges = priceRanges.replace("[", "").replace("]", "").split(",");
        List<String> validRanges = (List<String>)Arrays.<String>stream(ranges).map(String::trim).filter(range -> (!range.isEmpty() && range.contains("-"))).collect(Collectors.toList());
        if (!validRanges.isEmpty()) {
          List<BigDecimal[]> priceRangeList = (List)new ArrayList<>();
          for (String range : validRanges) {
            String[] parts = range.split("-");
            if (parts.length == 2)
              try {
                BigDecimal min = new BigDecimal(parts[0].trim());
                BigDecimal max = new BigDecimal(parts[1].trim());
                if (min.compareTo(BigDecimal.ZERO) >= 0 && max.compareTo(min) >= 0)
                  priceRangeList.add(new BigDecimal[] { min, max }); 
              } catch (NumberFormatException e) {
                log.warn("价格范围格式错误: {}", range);
              }  
          } 
          if (!priceRangeList.isEmpty())
            wrapper.and(w -> {
                  for (int i = 0; i < priceRangeList.size(); i++) {
                    BigDecimal[] priceRange = priceRangeList.get(i);
                    if (i == 0) {
                      w.between(Flower::getPrice, priceRange[0], priceRange[1]);
                    } else {
                      w.or().between(Flower::getPrice, priceRange[0], priceRange[1]);
                    } 
                  } 
                }); 
        } 
      } 
      if (stockRanges != null && !stockRanges.trim().isEmpty()) {
        String[] ranges = stockRanges.replace("[", "").replace("]", "").split(",");
        List<String> validRanges = (List<String>)Arrays.<String>stream(ranges).map(String::trim).filter(range -> (!range.isEmpty() && range.contains("-"))).collect(Collectors.toList());
        if (!validRanges.isEmpty()) {
          List<Integer[]> stockRangeList = (List)new ArrayList<>();
          for (String range : validRanges) {
            String[] parts = range.split("-");
            if (parts.length == 2)
              try {
                Integer min = Integer.valueOf(Integer.parseInt(parts[0].trim()));
                Integer max = Integer.valueOf(Integer.parseInt(parts[1].trim()));
                if (min.intValue() >= 0 && max.intValue() >= min.intValue())
                  stockRangeList.add(new Integer[] { min, max }); 
              } catch (NumberFormatException e) {
                log.warn("库存范围格式错误: {}", range);
              }  
          } 
          if (!stockRangeList.isEmpty())
            wrapper.and(w -> {
                  for (int i = 0; i < stockRangeList.size(); i++) {
                    Integer[] stockRange = stockRangeList.get(i);
                    if (i == 0) {
                      w.between(Flower::getStockQuantity, stockRange[0], stockRange[1]);
                    } else {
                      w.or().between(Flower::getStockQuantity, stockRange[0], stockRange[1]);
                    } 
                  } 
                }); 
        } 
      } 
      wrapper.orderByDesc(Flower::getCreatedAt);
      Page<Flower> result = (Page<Flower>)this.flowerMapper.selectPage((IPage)page, (Wrapper)wrapper);
      List<FlowerVO> flowerVOs = (List<FlowerVO>)result.getRecords().stream().map(flower -> {
            FlowerVO vo = FlowerVO.fromFlower(flower);
            if (flower.getCategoryId() != null) {
              Category category = (Category)this.categoryMapper.selectById(flower.getCategoryId());
              if (category != null)
                vo.setCategoryName(category.getName()); 
            } 
            return vo;
          }).collect(Collectors.toList());
      return Result.success(new PageResult(flowerVOs, 
            
            Long.valueOf(result.getTotal()), 
            Long.valueOf(result.getCurrent()), 
            Long.valueOf(result.getSize())));
    } catch (Exception e) {
      log.error("获取商品列表失败", e);
      return Result.error("获取商品列表失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/flowers/{id}"})
  public Result<FlowerVO> getFlowerDetail(@PathVariable Long id) {
    try {
      Flower flower = (Flower)this.flowerMapper.selectById(id);
      if (flower == null)
        return Result.notFound("商品不存在"); 
      FlowerVO vo = FlowerVO.fromFlower(flower);
      if (flower.getCategoryId() != null) {
        Category category = (Category)this.categoryMapper.selectById(flower.getCategoryId());
        if (category != null)
          vo.setCategoryName(category.getName()); 
      } 
      return Result.success(vo);
    } catch (Exception e) {
      log.error("获取商品详情失败", e);
      return Result.error("获取商品详情失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/flowers"})
  public Result<Flower> createFlower(@RequestBody Map<String, Object> flowerData) {
    try {
      String name = (String)flowerData.get("name");
      if (name == null || name.trim().isEmpty())
        return Result.paramError("商品名称不能为空"); 
      Object categoryIdObj = flowerData.get("categoryId");
      if (categoryIdObj == null)
        return Result.paramError("商品分类不能为空"); 
      Long categoryId = Long.valueOf(categoryIdObj.toString());
      Object priceObj = flowerData.get("price");
      if (priceObj == null)
        return Result.paramError("商品价格不能为空"); 
      BigDecimal price = new BigDecimal(priceObj.toString());
      if (price.compareTo(BigDecimal.ZERO) <= 0)
        return Result.paramError("商品价格必须大于0"); 
      Category category = (Category)this.categoryMapper.selectById(categoryId);
      if (category == null)
        return Result.paramError("商品分类不存在"); 
      Flower flower = new Flower();
      flower.setName(name.trim());
      flower.setCategoryId(categoryId);
      flower.setPrice(price);
      if (flowerData.containsKey("description"))
        flower.setDescription((String)flowerData.get("description")); 
      if (flowerData.containsKey("originalPrice")) {
        Object originalPriceObj = flowerData.get("originalPrice");
        if (originalPriceObj != null && !originalPriceObj.toString().isEmpty())
          flower.setOriginalPrice(new BigDecimal(originalPriceObj.toString())); 
      } 
      if (flowerData.containsKey("stockQuantity")) {
        flower.setStockQuantity(Integer.valueOf(flowerData.get("stockQuantity").toString()));
      } else {
        flower.setStockQuantity(Integer.valueOf(0));
      } 
      if (flowerData.containsKey("status")) {
        flower.setStatus(Integer.valueOf(flowerData.get("status").toString()));
      } else {
        flower.setStatus(Integer.valueOf(1));
      } 
      if (flowerData.containsKey("mainImage"))
        flower.setMainImage((String)flowerData.get("mainImage")); 
      if (flowerData.containsKey("detailImages"))
        flower.setImages((String)flowerData.get("detailImages")); 
      if (flowerData.containsKey("tags"))
        flower.setTags((String)flowerData.get("tags")); 
      if (flowerData.containsKey("flowerLanguage"))
        flower.setFlowerLanguage((String)flowerData.get("flowerLanguage")); 
      if (flowerData.containsKey("careInstructions"))
        flower.setCareInstructions((String)flowerData.get("careInstructions")); 
      if (flowerData.containsKey("occasion"))
        flower.setOccasion((String)flowerData.get("occasion")); 
      if (flowerData.containsKey("color"))
        flower.setColor((String)flowerData.get("color")); 
      if (flowerData.containsKey("size"))
        flower.setSize((String)flowerData.get("size")); 
      if (flowerData.containsKey("isFeatured")) {
        flower.setIsFeatured(Integer.valueOf(flowerData.get("isFeatured").toString()));
      } else {
        flower.setIsFeatured(Integer.valueOf(0));
      } 
      flower.setSalesCount(Integer.valueOf(0));
      flower.setCreatedAt(LocalDateTime.now());
      flower.setUpdatedAt(LocalDateTime.now());
      this.flowerMapper.insert(flower);
      return Result.success("商品创建成功", flower);
    } catch (Exception e) {
      log.error("创建商品失败", e);
      return Result.error("创建商品失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/flowers/{id}"})
  public Result<Flower> updateFlower(@PathVariable Long id, @RequestBody Map<String, Object> flowerData) {
    try {
      log.info("更新商品请求，ID: {}, 数据: {}", id, flowerData);
      log.info("接收到的字段: {}", flowerData.keySet());
      Flower existing = (Flower)this.flowerMapper.selectById(id);
      if (existing == null)
        return Result.notFound("商品不存在"); 
      log.info("更新前的商品数据: {}", existing);
      String name = (String)flowerData.get("name");
      if (name != null && name.trim().isEmpty())
        return Result.paramError("商品名称不能为空"); 
      Object priceObj = flowerData.get("price");
      if (priceObj != null) {
        BigDecimal price = new BigDecimal(priceObj.toString());
        if (price.compareTo(BigDecimal.ZERO) <= 0)
          return Result.paramError("商品价格必须大于0"); 
      } 
      Object categoryIdObj = flowerData.get("categoryId");
      if (categoryIdObj != null) {
        Long categoryId = Long.valueOf(categoryIdObj.toString());
        Category category = (Category)this.categoryMapper.selectById(categoryId);
        if (category == null)
          return Result.paramError("商品分类不存在"); 
        existing.setCategoryId(categoryId);
      } 
      if (flowerData.containsKey("name"))
        existing.setName(((String)flowerData.get("name")).trim()); 
      if (flowerData.containsKey("description"))
        existing.setDescription((String)flowerData.get("description")); 
      if (flowerData.containsKey("price"))
        existing.setPrice(new BigDecimal(flowerData.get("price").toString())); 
      if (flowerData.containsKey("originalPrice")) {
        Object originalPriceObj = flowerData.get("originalPrice");
        if (originalPriceObj != null)
          existing.setOriginalPrice(new BigDecimal(originalPriceObj.toString())); 
      } 
      if (flowerData.containsKey("stockQuantity"))
        existing.setStockQuantity(Integer.valueOf(flowerData.get("stockQuantity").toString())); 
      if (flowerData.containsKey("status"))
        existing.setStatus(Integer.valueOf(flowerData.get("status").toString())); 
      if (flowerData.containsKey("mainImage"))
        existing.setMainImage((String)flowerData.get("mainImage")); 
      if (flowerData.containsKey("detailImages"))
        existing.setImages((String)flowerData.get("detailImages")); 
      if (flowerData.containsKey("tags"))
        existing.setTags((String)flowerData.get("tags")); 
      if (flowerData.containsKey("flowerLanguage"))
        existing.setFlowerLanguage((String)flowerData.get("flowerLanguage")); 
      if (flowerData.containsKey("careInstructions"))
        existing.setCareInstructions((String)flowerData.get("careInstructions")); 
      if (flowerData.containsKey("occasion"))
        existing.setOccasion((String)flowerData.get("occasion")); 
      if (flowerData.containsKey("color"))
        existing.setColor((String)flowerData.get("color")); 
      if (flowerData.containsKey("size"))
        existing.setSize((String)flowerData.get("size")); 
      if (flowerData.containsKey("isFeatured"))
        existing.setIsFeatured(Integer.valueOf(flowerData.get("isFeatured").toString())); 
      existing.setUpdatedAt(LocalDateTime.now());
      log.info("更新后的商品数据: {}", existing);
      this.flowerMapper.updateById(existing);
      log.info("数据库更新完成");
      return Result.success("商品更新成功", existing);
    } catch (Exception e) {
      log.error("更新商品失败", e);
      return Result.error("更新商品失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/flowers/{id}/status"})
  public Result<String> updateFlowerStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
    try {
      Integer status = params.get("status");
      if (status == null)
        return Result.paramError("状态参数不能为空"); 
      Flower flower = (Flower)this.flowerMapper.selectById(id);
      if (flower == null)
        return Result.notFound("商品不存在"); 
      flower.setStatus(status);
      flower.setUpdatedAt(LocalDateTime.now());
      this.flowerMapper.updateById(flower);
      String statusText = (status.intValue() == 1) ? "上架" : "下架";
      return Result.success("商品已" + statusText);
    } catch (Exception e) {
      log.error("更新商品状态失败", e);
      return Result.error("更新商品状态失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/flowers/{id}"})
  public Result<String> deleteFlower(@PathVariable Long id) {
    try {
      Flower flower = (Flower)this.flowerMapper.selectById(id);
      if (flower == null)
        return Result.notFound("商品不存在"); 
      this.flowerMapper.deleteById(id);
      return Result.success("商品删除成功");
    } catch (Exception e) {
      log.error("删除商品失败", e);
      return Result.error("删除商品失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/reviews"})
  public Result<PageResult<Map<String, Object>>> getReviews(@RequestParam(defaultValue = "1") Long current, @RequestParam(defaultValue = "10") Long size, @RequestParam(required = false) String keyword, @RequestParam(required = false) Integer status, @RequestParam(required = false) Integer rating) {
    try {
      Page<FlowerReview> page = new Page<>(current.longValue(), size.longValue());
      LambdaQueryWrapper<FlowerReview> wrapper = new LambdaQueryWrapper();
      if (status != null)
        wrapper.eq(FlowerReview::getStatus, status); 
      if (rating != null)
        wrapper.eq(FlowerReview::getRating, rating); 
      wrapper.orderByDesc(FlowerReview::getCreatedAt);
      Page<FlowerReview> reviewPage = (Page<FlowerReview>)this.flowerReviewMapper.selectPage((IPage)page, (Wrapper)wrapper);
      List<Map<String, Object>> reviewList = new ArrayList<>();
      for (FlowerReview review : reviewPage.getRecords()) {
        Map<String, Object> reviewMap = new HashMap<>();
        reviewMap.put("id", review.getId());
        reviewMap.put("userId", review.getUserId());
        reviewMap.put("flowerId", review.getFlowerId());
        reviewMap.put("orderId", review.getOrderId());
        reviewMap.put("rating", review.getRating());
        reviewMap.put("content", review.getContent());
        reviewMap.put("images", review.getImages());
        reviewMap.put("status", review.getStatus());
        reviewMap.put("createdAt", review.getCreatedAt());
        User user = (User)this.userMapper.selectById(review.getUserId());
        if (user != null) {
          reviewMap.put("userName", (user.getNickname() != null) ? user.getNickname() : "匿名用户");
          reviewMap.put("userAvatar", user.getAvatarUrl());
        } else {
          reviewMap.put("userName", "匿名用户");
          reviewMap.put("userAvatar", "");
        } 
        Flower flower = (Flower)this.flowerMapper.selectById(review.getFlowerId());
        if (flower != null) {
          reviewMap.put("flowerName", flower.getName());
        } else {
          reviewMap.put("flowerName", "商品已删除");
        } 
        if (keyword != null && !keyword.trim().isEmpty()) {
          String searchKeyword = keyword.toLowerCase();
          String userName = (String)reviewMap.get("userName");
          String flowerName = (String)reviewMap.get("flowerName");
          String content = review.getContent();
          if ((userName != null && userName.toLowerCase().contains(searchKeyword)) || (flowerName != null && flowerName
            .toLowerCase().contains(searchKeyword)) || (content != null && content
            .toLowerCase().contains(searchKeyword)))
            reviewList.add(reviewMap); 
          continue;
        } 
        reviewList.add(reviewMap);
      } 
      return Result.success(new PageResult(reviewList, 
            
            Long.valueOf(reviewPage.getTotal()), 
            Long.valueOf(reviewPage.getCurrent()), 
            Long.valueOf(reviewPage.getSize())));
    } catch (Exception e) {
      log.error("获取评价列表失败", e);
      return Result.error("获取评价列表失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/reviews/{id}/status"})
  public Result<String> updateReviewStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
    try {
      Integer status = params.get("status");
      if (status == null)
        return Result.paramError("状态参数不能为空"); 
      FlowerReview review = (FlowerReview)this.flowerReviewMapper.selectById(id);
      if (review == null)
        return Result.notFound("评价不存在"); 
      review.setStatus(status);
      review.setUpdatedAt(LocalDateTime.now());
      this.flowerReviewMapper.updateById(review);
      String statusText = (status.intValue() == 1) ? "通过" : "拒绝";
      return Result.success("评价审核" + statusText);
    } catch (Exception e) {
      log.error("更新评价状态失败", e);
      return Result.error("更新评价状态失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/reviews/{id}"})
  public Result<String> deleteReview(@PathVariable Long id) {
    try {
      FlowerReview review = (FlowerReview)this.flowerReviewMapper.selectById(id);
      if (review == null)
        return Result.notFound("评价不存在"); 
      this.flowerReviewMapper.deleteById(id);
      return Result.success("评价删除成功");
    } catch (Exception e) {
      log.error("删除评价失败", e);
      return Result.error("删除评价失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/reviews/batch"})
  public Result<String> batchDeleteReviews(@RequestBody Map<String, List<Long>> params) {
    try {
      List<Long> ids = params.get("ids");
      if (ids == null || ids.isEmpty())
        return Result.paramError("评价ID列表不能为空"); 
      int deletedCount = this.flowerReviewMapper.deleteBatchIds(ids);
      return Result.success("成功删除 " + deletedCount + " 条评价");
    } catch (Exception e) {
      log.error("批量删除评价失败", e);
      return Result.error("批量删除评价失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/provinces"})
  public Result<List<Province>> getProvinces() {
    try {
      List<Province> provinces = this.regionMapper.getAllProvinces();
      return Result.success(provinces);
    } catch (Exception e) {
      log.error("获取省份列表失败", e);
      return Result.error("获取省份列表失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/cities/{provinceCode}"})
  public Result<List<City>> getCities(@PathVariable String provinceCode) {
    try {
      List<City> cities = this.regionMapper.getCitiesByProvinceCode(provinceCode);
      return Result.success(cities);
    } catch (Exception e) {
      log.error("获取城市列表失败", e);
      return Result.error("获取城市列表失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/districts/{cityCode}"})
  public Result<List<District>> getDistricts(@PathVariable String cityCode) {
    try {
      List<District> districts = this.regionMapper.getDistrictsByCityCode(cityCode);
      return Result.success(districts);
    } catch (Exception e) {
      log.error("获取区县列表失败", e);
      return Result.error("获取区县列表失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/address-stats"})
  public Result<Map<String, Object>> getAddressStats() {
    try {
      Map<String, Object> stats = new HashMap<>();
      List<Province> provinces = this.regionMapper.getAllProvinces();
      stats.put("provinceCount", Integer.valueOf(provinces.size()));
      int cityCount = 0;
      for (Province province : provinces) {
        List<City> cities = this.regionMapper.getCitiesByProvinceCode(province.getCode());
        cityCount += cities.size();
      } 
      stats.put("cityCount", Integer.valueOf(cityCount));
      int districtCount = 0;
      for (Province province : provinces) {
        List<City> cities = this.regionMapper.getCitiesByProvinceCode(province.getCode());
        for (City city : cities) {
          List<District> districts = this.regionMapper.getDistrictsByCityCode(city.getCode());
          districtCount += districts.size();
        } 
      } 
      stats.put("districtCount", Integer.valueOf(districtCount));
      return Result.success(stats);
    } catch (Exception e) {
      log.error("获取地址统计信息失败", e);
      return Result.error("获取地址统计信息失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/provinces"})
  public Result<String> addProvince(@RequestBody Province province) {
    try {
      List<Province> existingProvinces = this.regionMapper.getAllProvinces();
      for (Province p : existingProvinces) {
        if (p.getCode().equals(province.getCode()))
          return Result.error("省份代码已存在"); 
      } 
      this.regionMapper.insertProvince(province);
      return Result.success("省份添加成功");
    } catch (Exception e) {
      log.error("添加省份失败", e);
      return Result.error("添加省份失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/provinces/{id}"})
  public Result<String> updateProvince(@PathVariable Integer id, @RequestBody Province province) {
    try {
      province.setId(id);
      this.regionMapper.updateProvince(province);
      return Result.success("省份更新成功");
    } catch (Exception e) {
      log.error("更新省份失败", e);
      return Result.error("更新省份失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/provinces/{id}"})
  public Result<String> deleteProvince(@PathVariable Integer id) {
    try {
      this.regionMapper.deleteProvince(id);
      return Result.success("省份删除成功");
    } catch (Exception e) {
      log.error("删除省份失败", e);
      return Result.error("删除省份失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/cities"})
  public Result<String> addCity(@RequestBody City city) {
    try {
      List<City> existingCities = this.regionMapper.getCitiesByProvinceCode(city.getProvinceCode());
      for (City c : existingCities) {
        if (c.getCode().equals(city.getCode()))
          return Result.error("城市代码已存在"); 
      } 
      this.regionMapper.insertCity(city);
      return Result.success("城市添加成功");
    } catch (Exception e) {
      log.error("添加城市失败", e);
      return Result.error("添加城市失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/cities/{id}"})
  public Result<String> updateCity(@PathVariable Integer id, @RequestBody City city) {
    try {
      city.setId(id);
      this.regionMapper.updateCity(city);
      return Result.success("城市更新成功");
    } catch (Exception e) {
      log.error("更新城市失败", e);
      return Result.error("更新城市失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/cities/{id}"})
  public Result<String> deleteCity(@PathVariable Integer id) {
    try {
      this.regionMapper.deleteCity(id);
      return Result.success("城市删除成功");
    } catch (Exception e) {
      log.error("删除城市失败", e);
      return Result.error("删除城市失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/districts"})
  public Result<String> addDistrict(@RequestBody District district) {
    try {
      List<District> existingDistricts = this.regionMapper.getDistrictsByCityCode(district.getCityCode());
      for (District d : existingDistricts) {
        if (d.getCode().equals(district.getCode()))
          return Result.error("区县代码已存在"); 
      } 
      this.regionMapper.insertDistrict(district);
      return Result.success("区县添加成功");
    } catch (Exception e) {
      log.error("添加区县失败", e);
      return Result.error("添加区县失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/districts/{id}"})
  public Result<String> updateDistrict(@PathVariable Integer id, @RequestBody District district) {
    try {
      district.setId(id);
      this.regionMapper.updateDistrict(district);
      return Result.success("区县更新成功");
    } catch (Exception e) {
      log.error("更新区县失败", e);
      return Result.error("更新区县失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/districts/{id}"})
  public Result<String> deleteDistrict(@PathVariable Integer id) {
    try {
      this.regionMapper.deleteDistrict(id);
      return Result.success("区县删除成功");
    } catch (Exception e) {
      log.error("删除区县失败", e);
      return Result.error("删除区县失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/price-categories"})
  public Result<PageResult<PriceCategory>> getPriceCategories(@RequestParam(defaultValue = "1") Long current, @RequestParam(defaultValue = "10") Long size, @RequestParam(required = false) String keyword, @RequestParam(required = false) Integer status) {
    try {
      PageResult<PriceCategory> result = this.priceCategoryService.getPriceCategoriesByPage(current, size, keyword, status);
      return Result.success(result);
    } catch (Exception e) {
      log.error("获取价格分类列表失败", e);
      return Result.error("获取价格分类列表失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/price-categories/{id}"})
  public Result<PriceCategory> getPriceCategoryById(@PathVariable Long id) {
    try {
      PriceCategory priceCategory = this.priceCategoryService.getPriceCategoryById(id);
      if (priceCategory != null)
        return Result.success(priceCategory); 
      return Result.error("价格分类不存在");
    } catch (Exception e) {
      log.error("获取价格分类失败", e);
      return Result.error("获取价格分类失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/price-categories"})
  public Result<String> createPriceCategory(@RequestBody PriceCategory priceCategory) {
    try {
      boolean success = this.priceCategoryService.createPriceCategory(priceCategory);
      if (success)
        return Result.success("价格分类创建成功"); 
      return Result.error("价格分类创建失败");
    } catch (Exception e) {
      log.error("创建价格分类失败", e);
      return Result.error("创建价格分类失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/price-categories/{id}"})
  public Result<String> updatePriceCategory(@PathVariable Long id, @RequestBody PriceCategory priceCategory) {
    try {
      priceCategory.setId(id);
      boolean success = this.priceCategoryService.updatePriceCategory(priceCategory);
      if (success)
        return Result.success("价格分类更新成功"); 
      return Result.error("价格分类更新失败");
    } catch (Exception e) {
      log.error("更新价格分类失败", e);
      return Result.error("更新价格分类失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/price-categories/{id}"})
  public Result<String> deletePriceCategory(@PathVariable Long id) {
    try {
      boolean success = this.priceCategoryService.deletePriceCategory(id);
      if (success)
        return Result.success("价格分类删除成功"); 
      return Result.error("价格分类删除失败");
    } catch (Exception e) {
      log.error("删除价格分类失败", e);
      return Result.error("删除价格分类失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/price-categories/{id}/status"})
  public Result<String> updatePriceCategoryStatus(@PathVariable Long id, @RequestBody Map<String, Integer> requestBody) {
    try {
      Integer status = requestBody.get("status");
      if (status == null)
        return Result.error("状态参数不能为空"); 
      boolean success = this.priceCategoryService.updatePriceCategoryStatus(id, status);
      if (success)
        return Result.success("价格分类状态更新成功"); 
      return Result.error("价格分类状态更新失败");
    } catch (Exception e) {
      log.error("更新价格分类状态失败", e);
      return Result.error("更新价格分类状态失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/price-categories/active"})
  public Result<List<PriceCategory>> getActivePriceCategories() {
    try {
      List<PriceCategory> priceCategories = this.priceCategoryService.getAllActivePriceCategories();
      return Result.success(priceCategories);
    } catch (Exception e) {
      log.error("获取启用的价格分类失败", e);
      return Result.error("获取启用的价格分类失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/profile"})
  public Result<AdminUser> getProfile(HttpServletRequest request) {
    try {
      String token = getTokenFromRequest(request);
      if (token == null)
        return Result.error("未提供认证token"); 
      AdminUser admin = this.adminService.verifyToken(token);
      admin.setPassword(null);
      return Result.success(admin);
    } catch (Exception e) {
      log.error("获取管理员信息失败", e);
      return Result.error("获取管理员信息失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/profile"})
  public Result<String> updateProfile(@RequestBody AdminUser adminUser, HttpServletRequest request) {
    try {
      String token = getTokenFromRequest(request);
      if (token == null)
        return Result.error("未提供认证token"); 
      AdminUser currentAdmin = this.adminService.verifyToken(token);
      adminUser.setId(currentAdmin.getId());
      adminUser.setUpdatedAt(LocalDateTime.now());
      adminUser.setUsername(null);
      adminUser.setPassword(null);
      int result = this.adminUserMapper.updateById(adminUser);
      if (result > 0)
        return Result.success("更新成功"); 
      return Result.error("更新失败");
    } catch (Exception e) {
      log.error("更新管理员信息失败", e);
      return Result.error("更新管理员信息失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/password"})
  public Result<String> updatePassword(@RequestBody Map<String, String> passwordData, HttpServletRequest request) {
    try {
      String token = getTokenFromRequest(request);
      if (token == null)
        return Result.error("未提供认证token"); 
      AdminUser admin = this.adminService.verifyToken(token);
      if (admin == null)
        return Result.error("管理员不存在"); 
      String currentPassword = passwordData.get("currentPassword");
      String newPassword = passwordData.get("newPassword");
      if (currentPassword == null || currentPassword.trim().isEmpty())
        return Result.error("请输入当前密码"); 
      if (newPassword == null || newPassword.trim().isEmpty())
        return Result.error("请输入新密码"); 
      if (newPassword.length() < 6)
        return Result.error("新密码长度不能少于6位"); 
      boolean isCurrentPasswordValid = false;
      if (admin.getPassword().startsWith("$2a$") || admin.getPassword().startsWith("$2b$")) {
        isCurrentPasswordValid = PasswordUtil.matches(currentPassword, admin.getPassword());
      } else {
        isCurrentPasswordValid = admin.getPassword().equals(currentPassword);
      } 
      if (!isCurrentPasswordValid)
        return Result.error("当前密码错误"); 
      String encodedNewPassword = PasswordUtil.encode(newPassword);
      admin.setPassword(encodedNewPassword);
      admin.setUpdatedAt(LocalDateTime.now());
      int result = this.adminUserMapper.updateById(admin);
      if (result > 0)
        return Result.success("密码修改成功"); 
      return Result.error("密码修改失败");
    } catch (Exception e) {
      log.error("修改密码失败", e);
      return Result.error("修改密码失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/upload/avatar"})
  public Result<Map<String, String>> uploadAvatar(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
    try {
      String token = getTokenFromRequest(request);
      if (token == null)
        return Result.error("未提供认证token"); 
      AdminUser admin = this.adminService.verifyToken(token);
      Long adminId = admin.getId();
      Map<String, String> uploadResult = this.fileUploadService.uploadAdminAvatar(file, adminId);
      String avatarUrl = uploadResult.get("url");
      AdminUser updateAdmin = new AdminUser();
      updateAdmin.setId(adminId);
      updateAdmin.setAvatar(avatarUrl);
      updateAdmin.setUpdatedAt(LocalDateTime.now());
      this.adminUserMapper.updateById(updateAdmin);
      return Result.success(uploadResult);
    } catch (Exception e) {
      log.error("上传头像失败", e);
      return Result.error("上传头像失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/test/avatar/{filename}"})
  public Result<Map<String, Object>> testAvatarAccess(@PathVariable String filename) {
    try {
      String projectPath = System.getProperty("user.dir");
      String filePath = projectPath + "/image/admin-image/" + projectPath;
      File file = new File(filePath);
      Map<String, Object> result = new HashMap<>();
      result.put("filename", filename);
      result.put("filePath", filePath);
      result.put("fileExists", Boolean.valueOf(file.exists()));
      result.put("fileSize", Long.valueOf(file.exists() ? file.length() : 0L));
      result.put("accessUrl", "https://mxm.qiangs.xyz/api/image/admin-image/" + filename);
      result.put("projectPath", projectPath);
      return Result.success(result);
    } catch (Exception e) {
      log.error("测试头像访问失败", e);
      return Result.error("测试失败: " + e.getMessage());
    } 
  }
}
