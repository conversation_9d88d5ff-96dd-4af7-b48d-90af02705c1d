package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

@TableName("users")
public class User {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private String openid;
  
  private String unionid;
  
  @TableField(exist = false)
  private String sessionKey;
  
  private String nickname;
  
  private String avatarUrl;
  
  private String phone;
  
  private Integer gender;
  
  private String city;
  
  private String province;
  
  private String country;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  private Integer status;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setOpenid(String openid) {
    this.openid = openid;
  }
  
  public void setUnionid(String unionid) {
    this.unionid = unionid;
  }
  
  public void setSessionKey(String sessionKey) {
    this.sessionKey = sessionKey;
  }
  
  public void setNickname(String nickname) {
    this.nickname = nickname;
  }
  
  public void setAvatarUrl(String avatarUrl) {
    this.avatarUrl = avatarUrl;
  }
  
  public void setPhone(String phone) {
    this.phone = phone;
  }
  
  public void setGender(Integer gender) {
    this.gender = gender;
  }
  
  public void setCity(String city) {
    this.city = city;
  }
  
  public void setProvince(String province) {
    this.province = province;
  }
  
  public void setCountry(String country) {
    this.country = country;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public void setStatus(Integer status) {
    this.status = status;
  }
  
  public String toString() {
    return "User(id=" + getId() + ", openid=" + getOpenid() + ", unionid=" + getUnionid() + ", sessionKey=" + getSessionKey() + ", nickname=" + getNickname() + ", avatarUrl=" + getAvatarUrl() + ", phone=" + getPhone() + ", gender=" + getGender() + ", city=" + getCity() + ", province=" + getProvince() + ", country=" + getCountry() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ", status=" + getStatus() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.User))
      return false; 
    com.flower.entity.User other = (com.flower.entity.User)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$gender = getGender(), other$gender = other.getGender();
    if ((this$gender == null) ? (other$gender != null) : !this$gender.equals(other$gender))
      return false; 
    Object this$status = getStatus(), other$status = other.getStatus();
    if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status))
      return false; 
    Object this$openid = getOpenid(), other$openid = other.getOpenid();
    if ((this$openid == null) ? (other$openid != null) : !this$openid.equals(other$openid))
      return false; 
    Object this$unionid = getUnionid(), other$unionid = other.getUnionid();
    if ((this$unionid == null) ? (other$unionid != null) : !this$unionid.equals(other$unionid))
      return false; 
    Object this$sessionKey = getSessionKey(), other$sessionKey = other.getSessionKey();
    if ((this$sessionKey == null) ? (other$sessionKey != null) : !this$sessionKey.equals(other$sessionKey))
      return false; 
    Object this$nickname = getNickname(), other$nickname = other.getNickname();
    if ((this$nickname == null) ? (other$nickname != null) : !this$nickname.equals(other$nickname))
      return false; 
    Object this$avatarUrl = getAvatarUrl(), other$avatarUrl = other.getAvatarUrl();
    if ((this$avatarUrl == null) ? (other$avatarUrl != null) : !this$avatarUrl.equals(other$avatarUrl))
      return false; 
    Object this$phone = getPhone(), other$phone = other.getPhone();
    if ((this$phone == null) ? (other$phone != null) : !this$phone.equals(other$phone))
      return false; 
    Object this$city = getCity(), other$city = other.getCity();
    if ((this$city == null) ? (other$city != null) : !this$city.equals(other$city))
      return false; 
    Object this$province = getProvince(), other$province = other.getProvince();
    if ((this$province == null) ? (other$province != null) : !this$province.equals(other$province))
      return false; 
    Object this$country = getCountry(), other$country = other.getCountry();
    if ((this$country == null) ? (other$country != null) : !this$country.equals(other$country))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.User;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $gender = getGender();
    result = result * 59 + (($gender == null) ? 43 : $gender.hashCode());
    Object $status = getStatus();
    result = result * 59 + (($status == null) ? 43 : $status.hashCode());
    Object $openid = getOpenid();
    result = result * 59 + (($openid == null) ? 43 : $openid.hashCode());
    Object $unionid = getUnionid();
    result = result * 59 + (($unionid == null) ? 43 : $unionid.hashCode());
    Object $sessionKey = getSessionKey();
    result = result * 59 + (($sessionKey == null) ? 43 : $sessionKey.hashCode());
    Object $nickname = getNickname();
    result = result * 59 + (($nickname == null) ? 43 : $nickname.hashCode());
    Object $avatarUrl = getAvatarUrl();
    result = result * 59 + (($avatarUrl == null) ? 43 : $avatarUrl.hashCode());
    Object $phone = getPhone();
    result = result * 59 + (($phone == null) ? 43 : $phone.hashCode());
    Object $city = getCity();
    result = result * 59 + (($city == null) ? 43 : $city.hashCode());
    Object $province = getProvince();
    result = result * 59 + (($province == null) ? 43 : $province.hashCode());
    Object $country = getCountry();
    result = result * 59 + (($country == null) ? 43 : $country.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public String getOpenid() {
    return this.openid;
  }
  
  public String getUnionid() {
    return this.unionid;
  }
  
  public String getSessionKey() {
    return this.sessionKey;
  }
  
  public String getNickname() {
    return this.nickname;
  }
  
  public String getAvatarUrl() {
    return this.avatarUrl;
  }
  
  public String getPhone() {
    return this.phone;
  }
  
  public Integer getGender() {
    return this.gender;
  }
  
  public String getCity() {
    return this.city;
  }
  
  public String getProvince() {
    return this.province;
  }
  
  public String getCountry() {
    return this.country;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
  
  public Integer getStatus() {
    return this.status;
  }
}
