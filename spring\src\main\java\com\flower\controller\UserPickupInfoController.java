package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.UserPickupInfo;
import com.flower.service.UserPickupInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/pickup-info"})
@CrossOrigin(origins = {"*"})
public class UserPickupInfoController {
  private static final Logger log = LoggerFactory.getLogger(com.flower.controller.UserPickupInfoController.class);
  
  @Autowired
  private UserPickupInfoService userPickupInfoService;
  
  @GetMapping({"/{userId}"})
  public Result<UserPickupInfo> getUserPickupInfo(@PathVariable Long userId) {
    try {
      UserPickupInfo pickupInfo = this.userPickupInfoService.getByUserId(userId);
      return Result.success(pickupInfo);
    } catch (Exception e) {
      log.error("获取自取信息失败", e);
      return Result.error("获取自取信息失败");
    } 
  }
  
  @PostMapping({"/save"})
  public Result<UserPickupInfo> savePickupInfo(@RequestBody UserPickupInfo pickupInfo) {
    try {
      UserPickupInfo saved = this.userPickupInfoService.saveOrUpdate(pickupInfo);
      return Result.success("保存成功", saved);
    } catch (Exception e) {
      log.error("保存自取信息失败", e);
      return Result.error("保存失败: " + e.getMessage());
    } 
  }
  
  @DeleteMapping({"/{userId}"})
  public Result<String> deletePickupInfo(@PathVariable Long userId) {
    try {
      this.userPickupInfoService.deleteByUserId(userId);
      return Result.success("删除成功");
    } catch (Exception e) {
      log.error("删除自取信息失败", e);
      return Result.error("删除失败");
    } 
  }
}
