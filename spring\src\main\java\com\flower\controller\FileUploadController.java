package com.flower.controller;

import com.flower.common.Result;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping({"/admin/upload"})
@CrossOrigin(origins = {"*"})
public class FileUploadController {
  private static final Logger log = LoggerFactory.getLogger(com.flower.controller.FileUploadController.class);
  
  @Value("${server.port:8080}")
  private String serverPort;
  
  @Value("${server.servlet.context-path:/api}")
  private String contextPath;
  
  private static final String SHOP_IMAGE_PATH = System.getProperty("user.dir") + "/image/shop-image/";
  
  private static final Set<String> ALLOWED_EXTENSIONS = Set.of("jpg", "jpeg", "png", "gif", "webp");
  
  private static final long MAX_FILE_SIZE = 5242880L;
  
  @PostMapping({"/image"})
  public Result<Map<String, Object>> uploadImage(@RequestParam("file") MultipartFile file) {
    return uploadShopImage(file);
  }
  
  @PostMapping({"/shop-image"})
  public Result<Map<String, Object>> uploadShopImage(@RequestParam("file") MultipartFile file) {
    try {
      String validationError = validateFile(file);
      if (validationError != null)
        return Result.paramError(validationError); 
      String originalFilename = file.getOriginalFilename();
      String extension = getFileExtension(originalFilename);
      String newFileName = generateFileName(extension);
      File uploadDir = new File(SHOP_IMAGE_PATH);
      if (!uploadDir.exists()) {
        boolean created = uploadDir.mkdirs();
        if (!created) {
          log.error("创建上传目录失败: {}", SHOP_IMAGE_PATH);
          return Result.error("创建上传目录失败");
        } 
      } 
      File destFile = new File(uploadDir, newFileName);
      file.transferTo(destFile);
      String imageUrl = generateImageUrl(newFileName);
      Map<String, Object> result = new HashMap<>();
      result.put("fileName", newFileName);
      result.put("originalName", originalFilename);
      result.put("url", imageUrl);
      result.put("size", Long.valueOf(file.getSize()));
      result.put("uploadTime", LocalDateTime.now());
      log.info("商品图片上传成功: {} -> {}", originalFilename, imageUrl);
      return Result.success("图片上传成功", result);
    } catch (IOException e) {
      log.error("图片上传失败", e);
      return Result.error("图片上传失败: " + e.getMessage());
    } catch (Exception e) {
      log.error("图片上传异常", e);
      return Result.error("图片上传异常: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/shop-images"})
  public Result<List<Map<String, Object>>> uploadShopImages(@RequestParam("files") MultipartFile[] files) {
    if (files == null || files.length == 0)
      return Result.paramError("请选择要上传的图片"); 
    if (files.length > 10)
      return Result.paramError("一次最多只能上传10张图片"); 
    List<Map<String, Object>> results = new ArrayList<>();
    List<String> errors = new ArrayList<>();
    for (int i = 0; i < files.length; i++) {
      MultipartFile file = files[i];
      try {
        String validationError = validateFile(file);
        if (validationError != null) {
          errors.add("第" + i + 1 + "张图片: " + validationError);
        } else {
          String originalFilename = file.getOriginalFilename();
          String extension = getFileExtension(originalFilename);
          String newFileName = generateFileName(extension);
          File uploadDir = new File(SHOP_IMAGE_PATH);
          if (!uploadDir.exists())
            uploadDir.mkdirs(); 
          File destFile = new File(uploadDir, newFileName);
          file.transferTo(destFile);
          String imageUrl = generateImageUrl(newFileName);
          Map<String, Object> result = new HashMap<>();
          result.put("fileName", newFileName);
          result.put("originalName", originalFilename);
          result.put("url", imageUrl);
          result.put("size", Long.valueOf(file.getSize()));
          result.put("uploadTime", LocalDateTime.now());
          results.add(result);
        } 
      } catch (Exception e) {
        log.error("第{}张图片上传失败", Integer.valueOf(i + 1), e);
        errors.add("第" + i + 1 + "张图片上传失败: " + e.getMessage());
      } 
    } 
    if (!errors.isEmpty()) {
      String errorMessage = String.join("; ", (Iterable)errors);
      if (results.isEmpty())
        return Result.error("所有图片上传失败: " + errorMessage); 
      log.warn("部分图片上传失败: {}", errorMessage);
    } 
    String message = String.format("成功上传 %d 张图片", new Object[] { Integer.valueOf(results.size()) });
    if (!errors.isEmpty())
      message = message + message; 
    return Result.success(message, results);
  }
  
  @DeleteMapping({"/shop-image"})
  public Result<String> deleteShopImage(@RequestParam("fileName") String fileName) {
    try {
      if (fileName == null || fileName.trim().isEmpty())
        return Result.paramError("文件名不能为空"); 
      if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\"))
        return Result.paramError("文件名格式不正确"); 
      File file = new File(SHOP_IMAGE_PATH + SHOP_IMAGE_PATH);
      if (!file.exists())
        return Result.notFound("图片文件不存在"); 
      boolean deleted = file.delete();
      if (deleted) {
        log.info("商品图片删除成功: {}", fileName);
        return Result.success("图片删除成功");
      } 
      log.error("商品图片删除失败: {}", fileName);
      return Result.error("图片删除失败");
    } catch (Exception e) {
      log.error("删除图片异常", e);
      return Result.error("删除图片异常: " + e.getMessage());
    } 
  }
  
  private String validateFile(MultipartFile file) {
    if (file == null || file.isEmpty())
      return "请选择要上传的图片"; 
    if (file.getSize() > 5242880L)
      return "图片大小不能超过5MB"; 
    String originalFilename = file.getOriginalFilename();
    if (originalFilename == null || originalFilename.trim().isEmpty())
      return "文件名不能为空"; 
    String extension = getFileExtension(originalFilename);
    if (!ALLOWED_EXTENSIONS.contains(extension.toLowerCase()))
      return "只支持 jpg、jpeg、png、gif、webp 格式的图片"; 
    String contentType = file.getContentType();
    if (contentType == null || !contentType.startsWith("image/"))
      return "请上传图片文件"; 
    return null;
  }
  
  private String getFileExtension(String filename) {
    if (filename == null || filename.isEmpty())
      return ""; 
    int lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1)
      return ""; 
    return filename.substring(lastDotIndex + 1);
  }
  
  private String generateFileName(String extension) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    return String.format("shop_%s_%s.%s", new Object[] { timestamp, uuid, extension });
  }
  
  private String generateImageUrl(String fileName) {
    return String.format("https://mxm.qiangs.xyz/api/image/shop-image/%s", new Object[] { fileName });
  }
}
