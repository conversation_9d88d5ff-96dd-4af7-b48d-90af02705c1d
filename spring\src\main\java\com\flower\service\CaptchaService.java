package com.flower.service;

import com.flower.util.CaptchaUtil;
import com.flower.util.CaptchaResult;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class CaptchaService {
    private static final Logger log = LoggerFactory.getLogger(com.flower.service.CaptchaService.class);

    private final Map<String, CaptchaInfo> captchaStore = new ConcurrentHashMap<>();

    private static final int CAPTCHA_EXPIRE_MINUTES = 5;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    public CaptchaService() {
        this.scheduler.scheduleAtFixedRate(this::cleanExpiredCaptcha, 1L, 1L, TimeUnit.MINUTES);
    }

    public CaptchaResponse generateCaptcha() {
        try {
            CaptchaResult result = CaptchaUtil.generateCaptcha();
            String captchaId = UUID.randomUUID().toString();
            CaptchaInfo info = new CaptchaInfo(result.getCode(), System.currentTimeMillis());
            this.captchaStore.put(captchaId, info);
            log.info("生成验证码成功，ID: {}, 验证码: {}", captchaId, result.getCode());
            return new CaptchaResponse(captchaId, result.getImage());
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            throw new RuntimeException("生成验证码失败: " + e.getMessage());
        }
    }

    public boolean verifyCaptcha(String captchaId, String userInput) {
        try {
            if (captchaId == null || userInput == null) {
                log.warn("验证码ID或用户输入为空");
                return false;
            }
            CaptchaInfo info = this.captchaStore.get(captchaId);
            if (info == null) {
                log.warn("验证码不存在或已过期，ID: {}", captchaId);
                return false;
            }
            long now = System.currentTimeMillis();
            if (now - info.getCreateTime() > 300000L) {
                log.warn("验证码已过期，ID: {}", captchaId);
                this.captchaStore.remove(captchaId);
                return false;
            }
            boolean isValid = userInput.equalsIgnoreCase(info.getCode());
            this.captchaStore.remove(captchaId);
            log.info("验证码验证结果，ID: {}, 用户输入: {}, 正确答案: {}, 结果: {}", new Object[] { captchaId, userInput, info
                    .getCode(), Boolean.valueOf(isValid) });
            return isValid;
        } catch (Exception e) {
            log.error("验证验证码失败", e);
            return false;
        }
    }

    private void cleanExpiredCaptcha() {
        try {
            long now = System.currentTimeMillis();
            long expireTime = 300000L;
            this.captchaStore.entrySet().removeIf(entry -> {
                boolean expired = (now - ((CaptchaInfo)entry.getValue()).getCreateTime() > expireTime);
                if (expired)
                    log.debug("清理过期验证码，ID: {}", entry.getKey());
                return expired;
            });
        } catch (Exception e) {
            log.error("清理过期验证码失败", e);
        }
    }
}
