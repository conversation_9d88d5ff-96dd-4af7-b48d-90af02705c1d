package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.flower.entity.UserAddress;
import com.flower.mapper.UserAddressMapper;
import com.flower.service.AddressService;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AddressServiceImpl implements AddressService {
  @Autowired
  private UserAddressMapper addressMapper;
  
  public List<UserAddress> getUserAddresses(Long userId) {
    LambdaQueryWrapper<UserAddress> wrapper = new LambdaQueryWrapper();
    wrapper.eq(UserAddress::getUserId, userId);
    wrapper.orderByDesc(UserAddress::getIsDefault);
    wrapper.orderByDesc(UserAddress::getCreatedAt);
    return this.addressMapper.selectList((Wrapper)wrapper);
  }
  
  public UserAddress getAddressById(Long id) {
    return (UserAddress)this.addressMapper.selectById(id);
  }
  
  @Transactional
  public UserAddress addAddress(UserAddress address) {
    address.setCreatedAt(LocalDateTime.now());
    address.setUpdatedAt(LocalDateTime.now());
    if (address.getIsDefault() != null && address.getIsDefault().intValue() == 1)
      clearDefaultAddress(address.getUserId()); 
    this.addressMapper.insert(address);
    return address;
  }
  
  @Transactional
  public UserAddress updateAddress(UserAddress address) {
    address.setUpdatedAt(LocalDateTime.now());
    if (address.getIsDefault() != null && address.getIsDefault().intValue() == 1)
      clearDefaultAddress(address.getUserId()); 
    this.addressMapper.updateById(address);
    return address;
  }
  
  public Boolean deleteAddress(Long id, Long userId) {
    LambdaQueryWrapper<UserAddress> wrapper = new LambdaQueryWrapper();
    wrapper.eq(UserAddress::getId, id);
    wrapper.eq(UserAddress::getUserId, userId);
    UserAddress address = (UserAddress)this.addressMapper.selectOne((Wrapper)wrapper);
    if (address == null)
      return Boolean.valueOf(false); 
    int result = this.addressMapper.deleteById(id);
    return Boolean.valueOf((result > 0));
  }
  
  @Transactional
  public Boolean setDefaultAddress(Long id, Long userId) {
    LambdaQueryWrapper<UserAddress> wrapper = new LambdaQueryWrapper();
    wrapper.eq(UserAddress::getId, id);
    wrapper.eq(UserAddress::getUserId, userId);
    UserAddress address = (UserAddress)this.addressMapper.selectOne((Wrapper)wrapper);
    if (address == null)
      return Boolean.valueOf(false); 
    clearDefaultAddress(userId);
    LambdaUpdateWrapper<UserAddress> updateWrapper = new LambdaUpdateWrapper();
    updateWrapper.eq(UserAddress::getId, id);
    updateWrapper.set(UserAddress::getIsDefault, Integer.valueOf(1));
    updateWrapper.set(UserAddress::getUpdatedAt, LocalDateTime.now());
    int result = this.addressMapper.update(null, (Wrapper)updateWrapper);
    return Boolean.valueOf((result > 0));
  }
  
  public UserAddress getDefaultAddress(Long userId) {
    LambdaQueryWrapper<UserAddress> wrapper = new LambdaQueryWrapper();
    wrapper.eq(UserAddress::getUserId, userId);
    wrapper.eq(UserAddress::getIsDefault, Integer.valueOf(1));
    wrapper.last("LIMIT 1");
    return (UserAddress)this.addressMapper.selectOne((Wrapper)wrapper);
  }
  
  private void clearDefaultAddress(Long userId) {
    LambdaUpdateWrapper<UserAddress> updateWrapper = new LambdaUpdateWrapper();
    updateWrapper.eq(UserAddress::getUserId, userId);
    updateWrapper.set(UserAddress::getIsDefault, Integer.valueOf(0));
    updateWrapper.set(UserAddress::getUpdatedAt, LocalDateTime.now());
    this.addressMapper.update(null, (Wrapper)updateWrapper);
  }
}
