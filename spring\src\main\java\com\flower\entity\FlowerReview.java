package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

@TableName("flower_reviews")
public class FlowerReview {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private Long userId;
  
  private Long flowerId;
  
  private Long orderId;
  
  private Integer rating;
  
  private String content;
  
  private String images;
  
  private Integer isAnonymous;
  
  private Integer status;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setUserId(Long userId) {
    this.userId = userId;
  }
  
  public void setFlowerId(Long flowerId) {
    this.flowerId = flowerId;
  }
  
  public void setOrderId(Long orderId) {
    this.orderId = orderId;
  }
  
  public void setRating(Integer rating) {
    this.rating = rating;
  }
  
  public void setContent(String content) {
    this.content = content;
  }
  
  public void setImages(String images) {
    this.images = images;
  }
  
  public void setIsAnonymous(Integer isAnonymous) {
    this.isAnonymous = isAnonymous;
  }
  
  public void setStatus(Integer status) {
    this.status = status;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public String toString() {
    return "FlowerReview(id=" + getId() + ", userId=" + getUserId() + ", flowerId=" + getFlowerId() + ", orderId=" + getOrderId() + ", rating=" + getRating() + ", content=" + getContent() + ", images=" + getImages() + ", isAnonymous=" + getIsAnonymous() + ", status=" + getStatus() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.FlowerReview))
      return false; 
    com.flower.entity.FlowerReview other = (com.flower.entity.FlowerReview)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$userId = getUserId(), other$userId = other.getUserId();
    if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId))
      return false; 
    Object this$flowerId = getFlowerId(), other$flowerId = other.getFlowerId();
    if ((this$flowerId == null) ? (other$flowerId != null) : !this$flowerId.equals(other$flowerId))
      return false; 
    Object this$orderId = getOrderId(), other$orderId = other.getOrderId();
    if ((this$orderId == null) ? (other$orderId != null) : !this$orderId.equals(other$orderId))
      return false; 
    Object this$rating = getRating(), other$rating = other.getRating();
    if ((this$rating == null) ? (other$rating != null) : !this$rating.equals(other$rating))
      return false; 
    Object this$isAnonymous = getIsAnonymous(), other$isAnonymous = other.getIsAnonymous();
    if ((this$isAnonymous == null) ? (other$isAnonymous != null) : !this$isAnonymous.equals(other$isAnonymous))
      return false; 
    Object this$status = getStatus(), other$status = other.getStatus();
    if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status))
      return false; 
    Object this$content = getContent(), other$content = other.getContent();
    if ((this$content == null) ? (other$content != null) : !this$content.equals(other$content))
      return false; 
    Object this$images = getImages(), other$images = other.getImages();
    if ((this$images == null) ? (other$images != null) : !this$images.equals(other$images))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.FlowerReview;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $userId = getUserId();
    result = result * 59 + (($userId == null) ? 43 : $userId.hashCode());
    Object $flowerId = getFlowerId();
    result = result * 59 + (($flowerId == null) ? 43 : $flowerId.hashCode());
    Object $orderId = getOrderId();
    result = result * 59 + (($orderId == null) ? 43 : $orderId.hashCode());
    Object $rating = getRating();
    result = result * 59 + (($rating == null) ? 43 : $rating.hashCode());
    Object $isAnonymous = getIsAnonymous();
    result = result * 59 + (($isAnonymous == null) ? 43 : $isAnonymous.hashCode());
    Object $status = getStatus();
    result = result * 59 + (($status == null) ? 43 : $status.hashCode());
    Object $content = getContent();
    result = result * 59 + (($content == null) ? 43 : $content.hashCode());
    Object $images = getImages();
    result = result * 59 + (($images == null) ? 43 : $images.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public Long getUserId() {
    return this.userId;
  }
  
  public Long getFlowerId() {
    return this.flowerId;
  }
  
  public Long getOrderId() {
    return this.orderId;
  }
  
  public Integer getRating() {
    return this.rating;
  }
  
  public String getContent() {
    return this.content;
  }
  
  public String getImages() {
    return this.images;
  }
  
  public Integer getIsAnonymous() {
    return this.isAnonymous;
  }
  
  public Integer getStatus() {
    return this.status;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
