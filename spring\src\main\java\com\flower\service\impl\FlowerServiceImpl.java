package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.entity.Category;
import com.flower.entity.Flower;
import com.flower.entity.SearchKeyword;
import com.flower.mapper.CategoryMapper;
import com.flower.mapper.FlowerMapper;
import com.flower.mapper.SearchKeywordMapper;
import com.flower.service.FlowerService;
import com.flower.vo.FlowerVO;
import java.lang.invoke.SerializedLambda;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class FlowerServiceImpl implements FlowerService {
  @Autowired
  private FlowerMapper flowerMapper;
  
  @Autowired
  private CategoryMapper categoryMapper;
  
  @Autowired
  private SearchKeywordMapper searchKeywordMapper;
  
  public List<Category> getAllCategories() {
    LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Category::getStatus, Integer.valueOf(1));
    wrapper.orderByAsc(Category::getSortOrder);
    return this.categoryMapper.selectList((Wrapper)wrapper);
  }
  
  public PageResult<FlowerVO> getFlowersByPage(Long current, Long size, Long categoryId, String keyword, BigDecimal minPrice, BigDecimal maxPrice, Boolean excludeFeatured) {
    Page<Flower> page = new Page<>(current.longValue(), size.longValue());
    LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Flower::getStatus, Integer.valueOf(1));
    if (categoryId != null)
      wrapper.eq(Flower::getCategoryId, categoryId); 
    if (StringUtils.hasText(keyword))
      wrapper.and((LambdaQueryWrapper<Flower> w) -> w.like(Flower::getName, keyword)
            .or().like(Flower::getDescription, keyword)
            .or().like(Flower::getTags, keyword));
    if (minPrice != null)
      wrapper.ge(Flower::getPrice, minPrice); 
    if (maxPrice != null && maxPrice.compareTo(new BigDecimal("99999")) < 0)
      wrapper.le(Flower::getPrice, maxPrice); 
    if (excludeFeatured != null && excludeFeatured.booleanValue())
      wrapper.eq(Flower::getIsFeatured, Integer.valueOf(0)); 
    wrapper.orderByDesc(Flower::getIsFeatured);
    wrapper.orderByDesc(Flower::getSalesCount);
    wrapper.orderByDesc(Flower::getCreatedAt);
    IPage<Flower> result = this.flowerMapper.selectPage(page, wrapper);
    List<FlowerVO> flowerVOs = result.getRecords().stream().map(flower -> {
          FlowerVO vo = FlowerVO.fromFlower(flower);
          if (flower.getCategoryId() != null) {
            Category category = (Category)this.categoryMapper.selectById(flower.getCategoryId());
            if (category != null)
              vo.setCategoryName(category.getName());
          } 
          return vo;
        }).collect(Collectors.toList());
    return PageResult.of(flowerVOs, Long.valueOf(result.getTotal()), Long.valueOf(result.getSize()), Long.valueOf(result.getCurrent()));
  }
  
  public Flower getFlowerById(Long id) {
    LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Flower::getId, id);
    wrapper.eq(Flower::getStatus, Integer.valueOf(1));
    return (Flower)this.flowerMapper.selectOne((Wrapper)wrapper);
  }
  
  public List<Flower> getFeaturedFlowers(Integer limit, Long categoryId, BigDecimal minPrice, BigDecimal maxPrice) {
    LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Flower::getStatus, Integer.valueOf(1));
    wrapper.eq(Flower::getIsFeatured, Integer.valueOf(1));
    if (categoryId != null)
      wrapper.eq(Flower::getCategoryId, categoryId); 
    if (minPrice != null)
      wrapper.ge(Flower::getPrice, minPrice); 
    if (maxPrice != null && maxPrice.compareTo(new BigDecimal("99999")) < 0)
      wrapper.le(Flower::getPrice, maxPrice); 
    wrapper.orderByDesc(Flower::getSalesCount);
    wrapper.orderByDesc(Flower::getCreatedAt);
    wrapper.last("LIMIT " + ((limit != null) ? limit.intValue() : 10));
    return this.flowerMapper.selectList((Wrapper)wrapper);
  }
  
  public PageResult<FlowerVO> searchFlowers(String keyword, Long current, Long size) {
    return getFlowersByPage(current, size, null, keyword, null, null, null);
  }
  
  public PageResult<FlowerVO> getFlowersByCategory(Long categoryId, Long current, Long size) {
    return getFlowersByPage(current, size, categoryId, null, null, null, null);
  }
  
  public List<String> getHotKeywords() {
    List<String> hotKeywords = new ArrayList<>();
    hotKeywords.add("保温杯");
    hotKeywords.add("伴手礼");
    hotKeywords.add("广告衫");
    hotKeywords.add("签字笔");
    hotKeywords.add("蚕丝被");
    hotKeywords.add("艾草枕");
    hotKeywords.add("U盘");
    hotKeywords.add("洗护用品");
    try {
      List<SearchKeyword> tops = this.searchKeywordMapper.topKeywords(5);
      for (SearchKeyword sk : tops) {
        String kw = sk.getKeyword();
        if (kw != null && kw.trim().length() > 0 && !hotKeywords.contains(kw))
          hotKeywords.add(kw); 
      } 
    } catch (Exception exception) {}
    return hotKeywords;
  }
}
