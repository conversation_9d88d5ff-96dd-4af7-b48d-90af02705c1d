package com.flower.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "wechat.miniprogram")
public class WeChatConfig {
  private String appId;
  
  private String appSecret;
  
  public void setAppId(String appId) {
    this.appId = appId;
  }
  
  public void setAppSecret(String appSecret) {
    this.appSecret = appSecret;
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.config.WeChatConfig))
      return false; 
    com.flower.config.WeChatConfig other = (com.flower.config.WeChatConfig)o;
    if (!other.canEqual(this))
      return false; 
    Object this$appId = getAppId(), other$appId = other.getAppId();
    if ((this$appId == null) ? (other$appId != null) : !this$appId.equals(other$appId))
      return false; 
    Object this$appSecret = getAppSecret(), other$appSecret = other.getAppSecret();
    return !((this$appSecret == null) ? (other$appSecret != null) : !this$appSecret.equals(other$appSecret));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.config.WeChatConfig;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $appId = getAppId();
    result = result * 59 + (($appId == null) ? 43 : $appId.hashCode());
    Object $appSecret = getAppSecret();
    return result * 59 + (($appSecret == null) ? 43 : $appSecret.hashCode());
  }
  
  public String toString() {
    return "WeChatConfig(appId=" + getAppId() + ", appSecret=" + getAppSecret() + ")";
  }
  
  public String getAppId() {
    return this.appId;
  }
  
  public String getAppSecret() {
    return this.appSecret;
  }
  
  public boolean isConfigValid() {
    return (this.appId != null && !this.appId.equals("your_app_id_here") && this.appSecret != null && 
      !this.appSecret.equals("your_app_secret_here"));
  }
}
