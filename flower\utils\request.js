// HTTP请求工具
const API = require('../config/api.js')

/**
 * 封装wx.request
 * @param {Object} options 请求参数
 */
function request(options) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        console.log('API请求成功:', options.url, res.data)
        
        // 检查HTTP状态码
        if (res.statusCode === 200) {
          // 检查业务状态码
          if (res.data && res.data.code === 200) {
            resolve(res.data.data)
          } else {
            console.error('业务错误:', res.data)
            reject(new Error(res.data.message || '请求失败'))
          }
        } else {
          console.error('HTTP错误:', res.statusCode)
          reject(new Error(`HTTP错误: ${res.statusCode}`))
        }
      },
      fail: (err) => {
        console.error('请求失败:', options.url, err)
        reject(err)
      }
    })
  })
}

/**
 * GET请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} header 请求头
 */
function get(url, data = {}, header = {}) {
  return request({
    url,
    method: 'GET',
    data,
    header
  })
}

/**
 * POST请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} header 请求头
 */
function post(url, data = {}, header = {}) {
  return request({
    url,
    method: 'POST',
    data,
    header
  })
}

/**
 * PUT请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} header 请求头
 */
function put(url, data = {}, header = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    header
  })
}

/**
 * DELETE请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} header 请求头
 */
function del(url, data = {}, header = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    header
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del
}
