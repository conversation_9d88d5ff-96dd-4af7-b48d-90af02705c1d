package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.UserPickupInfo;
import com.flower.mapper.UserPickupInfoMapper;
import com.flower.service.UserPickupInfoService;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserPickupInfoServiceImpl implements UserPickupInfoService {
  @Autowired
  private UserPickupInfoMapper userPickupInfoMapper;
  
  public UserPickupInfo getByUserId(Long userId) {
    LambdaQueryWrapper<UserPickupInfo> wrapper = new LambdaQueryWrapper();
    wrapper.eq(UserPickupInfo::getUserId, userId);
    return (UserPickupInfo)this.userPickupInfoMapper.selectOne((Wrapper)wrapper);
  }
  
  public UserPickupInfo saveOrUpdate(UserPickupInfo pickupInfo) {
    if (pickupInfo.getId() == null) {
      UserPickupInfo existing = getByUserId(pickupInfo.getUserId());
      if (existing != null) {
        existing.setPickupName(pickupInfo.getPickupName());
        existing.setPickupPhone(pickupInfo.getPickupPhone());
        existing.setPickupTime(pickupInfo.getPickupTime());
        existing.setRemark(pickupInfo.getRemark());
        existing.setBackupAddress(pickupInfo.getBackupAddress());
        existing.setUpdatedAt(LocalDateTime.now());
        this.userPickupInfoMapper.updateById(existing);
        return existing;
      } 
      pickupInfo.setCreatedAt(LocalDateTime.now());
      pickupInfo.setUpdatedAt(LocalDateTime.now());
      this.userPickupInfoMapper.insert(pickupInfo);
      return pickupInfo;
    } 
    pickupInfo.setUpdatedAt(LocalDateTime.now());
    this.userPickupInfoMapper.updateById(pickupInfo);
    return pickupInfo;
  }
  
  public void deleteByUserId(Long userId) {
    LambdaQueryWrapper<UserPickupInfo> wrapper = new LambdaQueryWrapper();
    wrapper.eq(UserPickupInfo::getUserId, userId);
    this.userPickupInfoMapper.delete((Wrapper)wrapper);
  }
}
