package com.flower.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;

class OrderNotificationData {
  private Long id;
  
  private String orderNo;
  
  private BigDecimal totalAmount;
  
  private String userNickname;
  
  private String recipientName;
  
  private String recipientPhone;
  
  private Integer status;
  
  private LocalDateTime createTime;
  
  public Long getId() {
    return this.id;
  }
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public String getOrderNo() {
    return this.orderNo;
  }
  
  public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
  }
  
  public BigDecimal getTotalAmount() {
    return this.totalAmount;
  }
  
  public void setTotalAmount(BigDecimal totalAmount) {
    this.totalAmount = totalAmount;
  }
  
  public String getUserNickname() {
    return this.userNickname;
  }
  
  public void setUserNickname(String userNickname) {
    this.userNickname = userNickname;
  }
  
  public String getRecipientName() {
    return this.recipientName;
  }
  
  public void setRecipientName(String recipientName) {
    this.recipientName = recipientName;
  }
  
  public String getRecipientPhone() {
    return this.recipientPhone;
  }
  
  public void setRecipientPhone(String recipientPhone) {
    this.recipientPhone = recipientPhone;
  }
  
  public Integer getStatus() {
    return this.status;
  }
  
  public void setStatus(Integer status) {
    this.status = status;
  }
  
  public LocalDateTime getCreateTime() {
    return this.createTime;
  }
  
  public void setCreateTime(LocalDateTime createTime) {
    this.createTime = createTime;
  }
}
