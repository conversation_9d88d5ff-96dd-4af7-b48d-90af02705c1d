package com.flower.service;

import com.flower.entity.User;
import org.springframework.web.multipart.MultipartFile;

public interface UserService {
  User wechatLogin(String paramString);
  
  User getUserByOpenid(String paramString);
  
  User updateUser(User paramUser);
  
  User updateUserPhone(Long paramLong, String paramString);
  
  User findById(Long paramLong);
  
  User save(User paramUser);
  
  String uploadAvatar(MultipartFile paramMultipartFile, Long paramLong);
  
  User updateUserProfile(Long paramLong, String paramString1, String paramString2, String paramString3);
}
