package com.flower.util;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;
import javax.imageio.ImageIO;

public class CaptchaUtil {
  private static final int WIDTH = 120;
  
  private static final int HEIGHT = 40;
  
  private static final int CODE_LENGTH = 4;
  
  private static final String CHARS = "ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789";
  
  public static CaptchaResult generateCaptcha() {
    BufferedImage image = new BufferedImage(120, 40, 1);
    Graphics2D g = image.createGraphics();
    g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
    Random random = new Random();
    g.setColor(new Color(240, 240, 240));
    g.fillRect(0, 0, 120, 40);
    StringBuilder code = new StringBuilder();
    int i;
    for (i = 0; i < 4; i++)
      code.append("ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789".charAt(random.nextInt("ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789".length()))); 
    g.setFont(new Font("Arial", 1, 20));
    for (i = 0; i < 4; i++) {
      g.setColor(new Color(random.nextInt(100), random.nextInt(100), random.nextInt(100)));
      int x = 20 + i * 20 + random.nextInt(10);
      int y = 25 + random.nextInt(10);
      double angle = (random.nextDouble() - 0.5D) * 0.5D;
      g.rotate(angle, x, y);
      g.drawString(String.valueOf(code.charAt(i)), x, y);
      g.rotate(-angle, x, y);
    } 
    for (i = 0; i < 5; i++) {
      g.setColor(new Color(random.nextInt(150), random.nextInt(150), random.nextInt(150)));
      int x1 = random.nextInt(120);
      int y1 = random.nextInt(40);
      int x2 = random.nextInt(120);
      int y2 = random.nextInt(40);
      g.drawLine(x1, y1, x2, y2);
    } 
    for (i = 0; i < 50; i++) {
      g.setColor(new Color(random.nextInt(200), random.nextInt(200), random.nextInt(200)));
      g.fillOval(random.nextInt(120), random.nextInt(40), 2, 2);
    } 
    g.dispose();
    String base64Image = imageToBase64(image);
    return new CaptchaResult(code.toString(), base64Image);
  }
  
  private static String imageToBase64(BufferedImage image) {
    try {
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      ImageIO.write(image, "png", baos);
      byte[] bytes = baos.toByteArray();
      return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
    } catch (IOException e) {
      throw new RuntimeException("图片转换失败", e);
    } 
  }
}
