package com.flower.service;

import com.flower.entity.AdminUser;

public interface AdminService {
  AdminUser login(String paramString1, String paramString2);
  
  AdminUser verifyToken(String paramString);
  
  String generateToken(AdminUser paramAdminUser);
  
  void updateLastLogin(Long paramLong, String paramString);
  
  void logAction(Long paramLong, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7);
}
