package com.flower.controller;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class DocsController {

  // 在 context-path=/api 下，访问路径为：/api/api-docs 或 /api/api-docs.html
  @GetMapping({"/api-docs", "/api-docs.html"})
  public ResponseEntity<Resource> docs() {
    try {
      ClassPathResource resource = new ClassPathResource("static/api-docs.html");
      if (!resource.exists()) {
        return ResponseEntity.notFound().build();
      }
      return ResponseEntity.ok().contentType(MediaType.TEXT_HTML).body(resource);
    } catch (Exception e) {
      return ResponseEntity.internalServerError().build();
    }
  }
}

