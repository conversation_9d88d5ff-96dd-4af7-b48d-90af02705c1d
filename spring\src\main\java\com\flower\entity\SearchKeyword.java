package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

@TableName("search_keywords")
public class SearchKeyword {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private String keyword;
  
  @TableField("search_count")
  private Long searchCount;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setKeyword(String keyword) {
    this.keyword = keyword;
  }
  
  public void setSearchCount(Long searchCount) {
    this.searchCount = searchCount;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public String toString() {
    return "SearchKeyword(id=" + getId() + ", keyword=" + getKeyword() + ", searchCount=" + getSearchCount() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.SearchKeyword))
      return false; 
    com.flower.entity.SearchKeyword other = (com.flower.entity.SearchKeyword)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$searchCount = getSearchCount(), other$searchCount = other.getSearchCount();
    if ((this$searchCount == null) ? (other$searchCount != null) : !this$searchCount.equals(other$searchCount))
      return false; 
    Object this$keyword = getKeyword(), other$keyword = other.getKeyword();
    if ((this$keyword == null) ? (other$keyword != null) : !this$keyword.equals(other$keyword))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.SearchKeyword;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $searchCount = getSearchCount();
    result = result * 59 + (($searchCount == null) ? 43 : $searchCount.hashCode());
    Object $keyword = getKeyword();
    result = result * 59 + (($keyword == null) ? 43 : $keyword.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public String getKeyword() {
    return this.keyword;
  }
  
  public Long getSearchCount() {
    return this.searchCount;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
