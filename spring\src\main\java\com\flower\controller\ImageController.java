package com.flower.controller;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/image"})
@CrossOrigin(origins = {"*"})
public class ImageController {
  private static final Logger log = LoggerFactory.getLogger(com.flower.controller.ImageController.class);
  
  @GetMapping({"/user-image/{filename}"})
  public ResponseEntity<Resource> getUserImage(@PathVariable String filename) {
    try {
      log.info("请求用户头像: {}", filename);
      ClassPathResource classPathResource = new ClassPathResource("image/user-image/" + filename);
      if (classPathResource.exists()) {
        log.info("从classpath加载头像: {}", filename);
        return createImageResponse((Resource)classPathResource, filename);
      } 
      String projectPath = System.getProperty("user.dir");
      Path imagePath = Paths.get(projectPath, new String[] { "image", "user-image", filename });
      log.info("尝试从文件系统加载头像: {}", imagePath.toString());
      if (Files.exists(imagePath, new java.nio.file.LinkOption[0])) {
        FileSystemResource fileResource = new FileSystemResource(imagePath.toFile());
        log.info("从文件系统加载头像成功: {}", filename);
        return createImageResponse((Resource)fileResource, filename);
      } 
      log.warn("头像文件不存在: {}", filename);
      return ResponseEntity.notFound().build();
    } catch (Exception e) {
      log.error("获取用户头像失败: {}", filename, e);
      return ResponseEntity.notFound().build();
    } 
  }
  
  @GetMapping({"/shop-image/{filename}"})
  public ResponseEntity<Resource> getShopImage(@PathVariable String filename) {
    try {
      log.info("请求商品图片: {}", filename);
      ClassPathResource classPathResource = new ClassPathResource("image/shop-image/" + filename);
      if (classPathResource.exists()) {
        log.info("从classpath加载商品图片: {}", filename);
        return createImageResponse((Resource)classPathResource, filename);
      } 
      String projectPath = System.getProperty("user.dir");
      Path imagePath = Paths.get(projectPath, new String[] { "image", "shop-image", filename });
      log.info("尝试从文件系统加载商品图片: {}", imagePath.toString());
      if (Files.exists(imagePath, new java.nio.file.LinkOption[0])) {
        FileSystemResource fileResource = new FileSystemResource(imagePath.toFile());
        log.info("从文件系统加载商品图片成功: {}", filename);
        return createImageResponse((Resource)fileResource, filename);
      } 
      log.warn("商品图片文件不存在: {}", filename);
      return ResponseEntity.notFound().build();
    } catch (Exception e) {
      log.error("获取商品图片失败: {}", filename, e);
      return ResponseEntity.internalServerError().build();
    } 
  }
  
  private ResponseEntity<Resource> createImageResponse(Resource resource, String filename) throws IOException {
    String contentType = getContentType(filename);
    return ((ResponseEntity.BodyBuilder)ResponseEntity.ok()
      .contentType(MediaType.parseMediaType(contentType))
      .header("Content-Disposition", new String[] { "inline; filename=\"" + filename + "\"" })).body(resource);
  }
  
  private String getContentType(String filename) {
    String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    switch (extension) {
      case "jpg":
      case "jpeg":
        return "image/jpeg";
      case "png":
        return "image/png";
      case "gif":
        return "image/gif";
      case "webp":
        return "image/webp";
    } 
    return "application/octet-stream";
  }
  
  @GetMapping({"/test"})
  public ResponseEntity<String> testImageAccess() {
    try {
      String projectPath = System.getProperty("user.dir");
      Path userImagePath = Paths.get(projectPath, new String[] { "image", "user-image" });
      StringBuilder result = new StringBuilder();
      result.append("项目路径: ").append(projectPath).append("\n");
      result.append("用户头像目录: ").append(userImagePath.toString()).append("\n");
      result.append("目录是否存在: ").append(Files.exists(userImagePath, new java.nio.file.LinkOption[0])).append("\n");
      if (Files.exists(userImagePath, new java.nio.file.LinkOption[0])) {
        result.append("目录中的文件:\n");
        Files.list(userImagePath).forEach(file -> result.append("  - ").append(file.getFileName()).append("\n"));
      } 
      return ResponseEntity.ok(result.toString());
    } catch (Exception e) {
      log.error("测试图片访问失败", e);
      return ResponseEntity.internalServerError().body("测试失败: " + e.getMessage());
    } 
  }
}
