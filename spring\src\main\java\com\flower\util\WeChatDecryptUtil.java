package com.flower.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.nio.charset.StandardCharsets;
import java.security.Provider;
import java.security.Security;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WeChatDecryptUtil {
  private static final Logger log = LoggerFactory.getLogger(com.flower.util.WeChatDecryptUtil.class);
  
  static {
    Security.addProvider((Provider)new BouncyCastleProvider());
  }
  
  public static String decrypt(String encryptedData, String sessionKey, String iv) {
    try {
      byte[] dataByte = Base64.getDecoder().decode(encryptedData);
      byte[] keyByte = Base64.getDecoder().decode(sessionKey);
      byte[] ivByte = Base64.getDecoder().decode(iv);
      Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
      SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
      IvParameterSpec ivSpec = new IvParameterSpec(ivByte);
      cipher.init(2, spec, ivSpec);
      byte[] resultByte = cipher.doFinal(dataByte);
      String result = new String(resultByte, StandardCharsets.UTF_8);
      log.info("微信数据解密成功");
      return result;
    } catch (Exception e) {
      log.error("微信数据解密失败", e);
      throw new RuntimeException("数据解密失败", e);
    } 
  }
  
  public static JSONObject decryptPhoneNumber(String encryptedData, String sessionKey, String iv) {
    try {
      String decryptedData = decrypt(encryptedData, sessionKey, iv);
      JSONObject phoneInfo = JSON.parseObject(decryptedData);
      log.info("手机号解密成功: {}", phoneInfo.getString("phoneNumber"));
      return phoneInfo;
    } catch (Exception e) {
      log.error("手机号解密失败", e);
      throw new RuntimeException("手机号解密失败", e);
    } 
  }
  
  public static JSONObject decryptUserInfo(String encryptedData, String sessionKey, String iv) {
    try {
      String decryptedData = decrypt(encryptedData, sessionKey, iv);
      JSONObject userInfo = JSON.parseObject(decryptedData);
      log.info("用户信息解密成功: {}", userInfo.getString("nickName"));
      return userInfo;
    } catch (Exception e) {
      log.error("用户信息解密失败", e);
      throw new RuntimeException("用户信息解密失败", e);
    } 
  }
}
