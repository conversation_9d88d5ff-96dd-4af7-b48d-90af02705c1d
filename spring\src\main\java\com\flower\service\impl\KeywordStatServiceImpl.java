package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.SearchKeyword;
import com.flower.mapper.SearchKeywordMapper;
import com.flower.service.KeywordStatService;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class KeywordStatServiceImpl implements KeywordStatService {
  @Autowired
  private SearchKeywordMapper searchKeywordMapper;
  
  public void recordSearch(String keyword) {
    if (!StringUtils.hasText(keyword))
      return; 
    String cleaned = keyword.trim();
    LambdaQueryWrapper<SearchKeyword> wrapper = new LambdaQueryWrapper();
    wrapper.eq(SearchKeyword::getKeyword, cleaned);
    SearchKeyword existing = (SearchKeyword)this.searchKeywordMapper.selectOne((Wrapper)wrapper);
    if (existing == null) {
      SearchKeyword sk = new SearchKeyword();
      sk.setKeyword(cleaned);
      sk.setSearchCount(Long.valueOf(1L));
      sk.setCreatedAt(LocalDateTime.now());
      sk.setUpdatedAt(LocalDateTime.now());
      this.searchKeywordMapper.insert(sk);
    } else {
      existing.setSearchCount(Long.valueOf((existing.getSearchCount() == null) ? 1L : (existing.getSearchCount().longValue() + 1L)));
      existing.setUpdatedAt(LocalDateTime.now());
      this.searchKeywordMapper.updateById(existing);
    } 
  }
}
