package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.flower.config.FileUploadConfig;
import com.flower.entity.Swiper;
import com.flower.mapper.SwiperMapper;
import com.flower.service.SwiperService;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
public class SwiperServiceImpl extends ServiceImpl<SwiperMapper, Swiper> implements SwiperService {
  @Autowired
  private SwiperMapper swiperMapper;
  
  @Autowired
  private FileUploadConfig fileUploadConfig;
  
  public Page<Swiper> getPage(int pageNum, int pageSize, String title, Integer status) {
    Page<Swiper> page = new Page(pageNum, pageSize);
    QueryWrapper<Swiper> queryWrapper = new QueryWrapper();
    if (StringUtils.hasText(title))
      queryWrapper.like("title", title); 
    if (status != null)
      queryWrapper.eq("status", status); 
    ((QueryWrapper)queryWrapper.orderByAsc("sort_order")).orderByDesc("create_time");
    return (Page<Swiper>)page((IPage)page, (Wrapper)queryWrapper);
  }
  
  public List<Swiper> getActiveSwipers() {
    return this.swiperMapper.getActiveSwipers();
  }
  
  public String uploadImage(MultipartFile file) {
    if (file.isEmpty())
      throw new RuntimeException("上传文件不能为空"); 
    String originalFilename = file.getOriginalFilename();
    if (originalFilename == null)
      throw new RuntimeException("文件名不能为空"); 
    if (file.getSize() > this.fileUploadConfig.getMaxFileSize())
      throw new RuntimeException("文件大小不能超过2MB"); 
    String extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
    boolean isValidType = false;
    for (String allowedType : this.fileUploadConfig.getAllowedImageTypes()) {
      if (extension.equals("." + allowedType)) {
        isValidType = true;
        break;
      } 
    } 
    if (!isValidType)
      throw new RuntimeException("不支持的文件格式，请上传jpg、png、gif格式的图片"); 
    String newFilename = UUID.randomUUID().toString() + UUID.randomUUID().toString();
    File uploadDir = new File(this.fileUploadConfig.getFullSwiperPath());
    if (!uploadDir.exists())
      uploadDir.mkdirs(); 
    File destFile = new File(uploadDir, newFilename);
    try {
      file.transferTo(destFile);
    } catch (IOException e) {
      throw new RuntimeException("文件上传失败: " + e.getMessage());
    } 
    return this.fileUploadConfig.getFullImageUrl(newFilename);
  }
  
  public boolean updateStatus(Integer id, Integer status) {
    Swiper swiper = new Swiper();
    swiper.setId(id);
    swiper.setStatus(status);
    swiper.setUpdateTime(LocalDateTime.now());
    return updateById(swiper);
  }
  
  public boolean updateSortOrder(Integer id, Integer sortOrder) {
    Swiper swiper = new Swiper();
    swiper.setId(id);
    swiper.setSortOrder(sortOrder);
    swiper.setUpdateTime(LocalDateTime.now());
    return updateById(swiper);
  }
}
