package com.flower.service;

import com.flower.entity.CartItem;
import java.util.List;

public interface CartService {
  CartItem addToCart(Long paramLong1, Long paramLong2, Integer paramInteger);
  
  CartItem updateCartItem(Long paramLong1, Long paramLong2, Integer paramInteger);
  
  Boolean removeFromCart(Long paramLong1, Long paramLong2);
  
  List<CartItem> getUserCartItems(Long paramLong);
  
  Boolean clearCart(Long paramLong);
  
  Integer batchRemoveFromCart(Long paramLong, List<Long> paramList);
  
  Integer getCartItemCount(Long paramLong);
}
