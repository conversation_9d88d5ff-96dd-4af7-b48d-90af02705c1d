package com.flower.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JwtUtil {
  private static final Logger log = LoggerFactory.getLogger(com.flower.util.JwtUtil.class);
  
  private static final String SECRET_KEY = "flower-admin-secret-key";
  
  private static final long EXPIRATION_TIME = 86400000L;
  
  public static String generateToken(Long adminId, String username, String role) {
    Date now = new Date();
    Date expiryDate = new Date(now.getTime() + 86400000L);
    return Jwts.builder()
      .setSubject(adminId.toString())
      .claim("username", username)
      .claim("role", role)
      .setIssuedAt(now)
      .setExpiration(expiryDate)
      .signWith(SignatureAlgorithm.HS512, "flower-admin-secret-key")
      .compact();
  }
  
  public static Long getAdminIdFromToken(String token) {
    try {
      Claims claims = (Claims)Jwts.parser().setSigningKey("flower-admin-secret-key").parseClaimsJws(token).getBody();
      return Long.valueOf(claims.getSubject());
    } catch (Exception e) {
      log.error("解析token失败", e);
      throw new RuntimeException("Token无效");
    } 
  }
  
  public static String getUsernameFromToken(String token) {
    try {
      Claims claims = (Claims)Jwts.parser().setSigningKey("flower-admin-secret-key").parseClaimsJws(token).getBody();
      return (String)claims.get("username", String.class);
    } catch (Exception e) {
      log.error("解析token失败", e);
      throw new RuntimeException("Token无效");
    } 
  }
  
  public static String getRoleFromToken(String token) {
    try {
      Claims claims = (Claims)Jwts.parser().setSigningKey("flower-admin-secret-key").parseClaimsJws(token).getBody();
      return (String)claims.get("role", String.class);
    } catch (Exception e) {
      log.error("解析token失败", e);
      throw new RuntimeException("Token无效");
    } 
  }
  
  public static boolean validateToken(String token) {
    try {
      Jwts.parser()
        .setSigningKey("flower-admin-secret-key")
        .parseClaimsJws(token);
      return true;
    } catch (Exception e) {
      log.error("Token验证失败", e);
      return false;
    } 
  }
  
  public static boolean isTokenExpired(String token) {
    try {
      Claims claims = (Claims)Jwts.parser().setSigningKey("flower-admin-secret-key").parseClaimsJws(token).getBody();
      Date expiration = claims.getExpiration();
      return expiration.before(new Date());
    } catch (Exception e) {
      log.error("检查token过期状态失败", e);
      return true;
    } 
  }
  
  public static Date getExpirationDateFromToken(String token) {
    try {
      Claims claims = (Claims)Jwts.parser().setSigningKey("flower-admin-secret-key").parseClaimsJws(token).getBody();
      return claims.getExpiration();
    } catch (Exception e) {
      log.error("获取token过期时间失败", e);
      throw new RuntimeException("Token无效");
    } 
  }
}
