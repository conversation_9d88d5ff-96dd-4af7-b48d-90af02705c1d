package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

@TableName("swiper")
public class Swiper {
  @TableId(type = IdType.AUTO)
  private Integer id;
  
  private String title;
  
  private String imageUrl;
  
  private String linkUrl;
  
  private Integer sortOrder;
  
  private Integer status;
  
  private String description;
  
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime createTime;
  
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime updateTime;
  
  public void setId(Integer id) {
    this.id = id;
  }
  
  public void setTitle(String title) {
    this.title = title;
  }
  
  public void setImageUrl(String imageUrl) {
    this.imageUrl = imageUrl;
  }
  
  public void setLinkUrl(String linkUrl) {
    this.linkUrl = linkUrl;
  }
  
  public void setSortOrder(Integer sortOrder) {
    this.sortOrder = sortOrder;
  }
  
  public void setStatus(Integer status) {
    this.status = status;
  }
  
  public void setDescription(String description) {
    this.description = description;
  }
  
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  public void setCreateTime(LocalDateTime createTime) {
    this.createTime = createTime;
  }
  
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  public void setUpdateTime(LocalDateTime updateTime) {
    this.updateTime = updateTime;
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.Swiper))
      return false; 
    com.flower.entity.Swiper other = (com.flower.entity.Swiper)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$sortOrder = getSortOrder(), other$sortOrder = other.getSortOrder();
    if ((this$sortOrder == null) ? (other$sortOrder != null) : !this$sortOrder.equals(other$sortOrder))
      return false; 
    Object this$status = getStatus(), other$status = other.getStatus();
    if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status))
      return false; 
    Object this$title = getTitle(), other$title = other.getTitle();
    if ((this$title == null) ? (other$title != null) : !this$title.equals(other$title))
      return false; 
    Object this$imageUrl = getImageUrl(), other$imageUrl = other.getImageUrl();
    if ((this$imageUrl == null) ? (other$imageUrl != null) : !this$imageUrl.equals(other$imageUrl))
      return false; 
    Object this$linkUrl = getLinkUrl(), other$linkUrl = other.getLinkUrl();
    if ((this$linkUrl == null) ? (other$linkUrl != null) : !this$linkUrl.equals(other$linkUrl))
      return false; 
    Object this$description = getDescription(), other$description = other.getDescription();
    if ((this$description == null) ? (other$description != null) : !this$description.equals(other$description))
      return false; 
    Object this$createTime = getCreateTime(), other$createTime = other.getCreateTime();
    if ((this$createTime == null) ? (other$createTime != null) : !this$createTime.equals(other$createTime))
      return false; 
    Object this$updateTime = getUpdateTime(), other$updateTime = other.getUpdateTime();
    return !((this$updateTime == null) ? (other$updateTime != null) : !this$updateTime.equals(other$updateTime));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.Swiper;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $sortOrder = getSortOrder();
    result = result * 59 + (($sortOrder == null) ? 43 : $sortOrder.hashCode());
    Object $status = getStatus();
    result = result * 59 + (($status == null) ? 43 : $status.hashCode());
    Object $title = getTitle();
    result = result * 59 + (($title == null) ? 43 : $title.hashCode());
    Object $imageUrl = getImageUrl();
    result = result * 59 + (($imageUrl == null) ? 43 : $imageUrl.hashCode());
    Object $linkUrl = getLinkUrl();
    result = result * 59 + (($linkUrl == null) ? 43 : $linkUrl.hashCode());
    Object $description = getDescription();
    result = result * 59 + (($description == null) ? 43 : $description.hashCode());
    Object $createTime = getCreateTime();
    result = result * 59 + (($createTime == null) ? 43 : $createTime.hashCode());
    Object $updateTime = getUpdateTime();
    return result * 59 + (($updateTime == null) ? 43 : $updateTime.hashCode());
  }
  
  public String toString() {
    return "Swiper(id=" + getId() + ", title=" + getTitle() + ", imageUrl=" + getImageUrl() + ", linkUrl=" + getLinkUrl() + ", sortOrder=" + getSortOrder() + ", status=" + getStatus() + ", description=" + getDescription() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + ")";
  }
  
  public Integer getId() {
    return this.id;
  }
  
  public String getTitle() {
    return this.title;
  }
  
  public String getImageUrl() {
    return this.imageUrl;
  }
  
  public String getLinkUrl() {
    return this.linkUrl;
  }
  
  public Integer getSortOrder() {
    return this.sortOrder;
  }
  
  public Integer getStatus() {
    return this.status;
  }
  
  public String getDescription() {
    return this.description;
  }
  
  public LocalDateTime getCreateTime() {
    return this.createTime;
  }
  
  public LocalDateTime getUpdateTime() {
    return this.updateTime;
  }
}
