package com.flower.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.User;
import com.flower.mapper.UserMapper;
import com.flower.service.UserService;
import java.io.File;
import java.io.IOException;
import java.lang.invoke.SerializedLambda;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class UserServiceImpl implements UserService {
  private static final Logger log = LoggerFactory.getLogger(com.flower.service.impl.UserServiceImpl.class);
  
  @Autowired
  private UserMapper userMapper;
  
  @Value("${wechat.miniprogram.app-id}")
  private String appId;
  
  @Value("${wechat.miniprogram.app-secret}")
  private String appSecret;
  
  @Value("${app.server.image-url-prefix}")
  private String imageUrlPrefix;
  
  public User wechatLogin(String code) {
    log.info("开始微信登录，code: {}, appId: {}", code, this.appId);
    if ("your_app_id_here".equals(this.appId) || "your_app_secret_here".equals(this.appSecret)) {
      log.warn("检测到开发环境配置，使用模拟登录");
      return mockWechatLogin(code);
    } 
    String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code", new Object[] { this.appId, this.appSecret, code });
    try {
      log.info("调用微信API: {}", url);
      String response = HttpUtil.get(url);
      JSONObject jsonObject = JSONUtil.parseObj(response);
      if (jsonObject.containsKey("errcode")) {
        log.error("微信登录错误: {}", response);
        log.warn("微信登录失败，尝试使用模拟登录");
        return mockWechatLogin(code);
      } 
      String openid = jsonObject.getStr("openid");
      String unionid = jsonObject.getStr("unionid");
      String sessionKey = jsonObject.getStr("session_key");
      return createOrUpdateUser(openid, unionid, sessionKey);
    } catch (Exception e) {
      log.error("微信登录异常，使用模拟登录", e);
      return mockWechatLogin(code);
    } 
  }
  
  private User mockWechatLogin(String code) {
    log.info("开始模拟微信登录，code: {}", code);
    String mockOpenid = "mock_openid_" + Math.abs(code.hashCode());
    String mockUnionid = "mock_unionid_" + Math.abs(code.hashCode());
    log.info("模拟登录 - OpenID: {}, UnionID: {}", mockOpenid, mockUnionid);
    try {
      String mockSessionKey = "mock_session_key_" + Math.abs(code.hashCode());
      User user = createOrUpdateUser(mockOpenid, mockUnionid, mockSessionKey);
      log.info("模拟登录成功，用户ID: {}", user.getId());
      return user;
    } catch (Exception e) {
      log.error("模拟登录失败", e);
      throw new RuntimeException("模拟登录失败: " + e.getMessage());
    } 
  }
  
  private User createOrUpdateUser(String openid, String unionid, String sessionKey) {
    User user = getUserByOpenid(openid);
    if (user == null) {
      user = new User();
      user.setOpenid(openid);
      user.setUnionid(unionid);
      user.setSessionKey(sessionKey);
      user.setNickname("微信用户" + System.currentTimeMillis() % 10000L);
      user.setStatus(Integer.valueOf(1));
      user.setCreatedAt(LocalDateTime.now());
      user.setUpdatedAt(LocalDateTime.now());
      this.userMapper.insert(user);
      log.info("创建新用户: {}", openid);
    } else {
      user.setSessionKey(sessionKey);
      user.setUpdatedAt(LocalDateTime.now());
      this.userMapper.updateById(user);
      log.info("用户登录: {}", openid);
    } 
    return user;
  }
  
  public User getUserByOpenid(String openid) {
    try {
      if (openid == null || openid.trim().isEmpty())
        throw new IllegalArgumentException("OpenID不能为空"); 
      LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper();
      wrapper.eq(User::getOpenid, openid);
      wrapper.eq(User::getStatus, Integer.valueOf(1));
      return (User)this.userMapper.selectOne((Wrapper)wrapper);
    } catch (Exception e) {
      log.error("根据OpenID获取用户失败: {}", openid, e);
      throw new RuntimeException("获取用户信息失败: " + e.getMessage(), e);
    } 
  }
  
  public User updateUser(User user) {
    try {
      if (user == null || user.getId() == null)
        throw new IllegalArgumentException("用户信息或用户ID不能为空"); 
      user.setUpdatedAt(LocalDateTime.now());
      int result = this.userMapper.updateById(user);
      if (result > 0)
        return (User)this.userMapper.selectById(user.getId()); 
      throw new RuntimeException("更新用户信息失败，用户不存在");
    } catch (Exception e) {
      log.error("更新用户信息失败: {}", (user != null) ? user.getId() : "null", e);
      throw new RuntimeException("更新用户信息失败: " + e.getMessage(), e);
    } 
  }
  
  public User updateUserPhone(Long userId, String phone) {
    User user = (User)this.userMapper.selectById(userId);
    if (user != null) {
      user.setPhone(phone);
      user.setUpdatedAt(LocalDateTime.now());
      this.userMapper.updateById(user);
    } 
    return user;
  }
  
  public User findById(Long userId) {
    return (User)this.userMapper.selectById(userId);
  }
  
  public User save(User user) {
    if (user.getId() == null) {
      user.setCreatedAt(LocalDateTime.now());
      user.setUpdatedAt(LocalDateTime.now());
      this.userMapper.insert(user);
    } else {
      user.setUpdatedAt(LocalDateTime.now());
      this.userMapper.updateById(user);
    } 
    return user;
  }
  
  public String uploadAvatar(MultipartFile file, Long userId) {
    try {
      String uploadDir = getUploadDirectory();
      File dir = new File(uploadDir);
      if (!dir.exists()) {
        log.info("目录不存在，尝试创建: {}", uploadDir);
        boolean created = dir.mkdirs();
        if (!created) {
          File parentDir = dir.getParentFile();
          log.error("创建目录失败 - 目录: {}", uploadDir);
          log.error("父目录存在: {}", Boolean.valueOf((parentDir != null && parentDir.exists())));
          log.error("父目录可写: {}", Boolean.valueOf((parentDir != null && parentDir.canWrite())));
          log.error("当前用户: {}", System.getProperty("user.name"));
          throw new RuntimeException("无法创建上传目录: " + uploadDir + " (父目录存在: " + ((parentDir != null && parentDir
              .exists()) ? 1 : 0) + ", 父目录可写: " + ((parentDir != null && parentDir
              .canWrite()) ? 1 : 0) + ")");
        } 
        log.info("目录创建成功: {}", uploadDir);
      } else {
        log.info("目录已存在: {}", uploadDir);
      } 
      String originalFilename = file.getOriginalFilename();
      String extension = (originalFilename != null) ? originalFilename.substring(originalFilename.lastIndexOf(".")) : ".jpg";
      String filename = "avatar_" + userId + "_" + System.currentTimeMillis() + extension;
      Path filePath = Paths.get(uploadDir, new String[] { filename });
      Files.copy(file.getInputStream(), filePath, new java.nio.file.CopyOption[0]);
      String relativePath = "/image/user-image/" + filename;
      String fullAvatarUrl = this.imageUrlPrefix + this.imageUrlPrefix;
      User user = (User)this.userMapper.selectById(userId);
      if (user != null) {
        deleteOldAvatar(user.getAvatarUrl(), uploadDir);
        user.setAvatarUrl(fullAvatarUrl);
        user.setUpdatedAt(LocalDateTime.now());
        this.userMapper.updateById(user);
      } 
      log.info("用户头像上传成功，userId: {}, avatarUrl: {}", userId, fullAvatarUrl);
      return fullAvatarUrl;
    } catch (IOException e) {
      log.error("头像上传失败，userId: {}", userId, e);
      throw new RuntimeException("头像上传失败: " + e.getMessage());
    } catch (Exception e) {
      log.error("头像上传失败，userId: {}", userId, e);
      throw new RuntimeException("头像上传失败: " + e.getMessage());
    } 
  }
  
  public User updateUserProfile(Long userId, String nickname, String avatarBase64, String phone) {
    try {
      User user = (User)this.userMapper.selectById(userId);
      if (user == null)
        throw new RuntimeException("用户不存在"); 
      if (nickname != null && !nickname.trim().isEmpty())
        user.setNickname(nickname.trim()); 
      if (phone != null && !phone.trim().isEmpty()) {
        if (!phone.matches("^1[3-9]\\d{9}$"))
          throw new RuntimeException("手机号格式不正确"); 
        user.setPhone(phone.trim());
      } 
      if (avatarBase64 != null && !avatarBase64.trim().isEmpty()) {
        String avatarUrl = saveBase64Avatar(avatarBase64, userId);
        user.setAvatarUrl(avatarUrl);
      } 
      user.setUpdatedAt(LocalDateTime.now());
      this.userMapper.updateById(user);
      log.info("用户信息更新成功，userId: {}, nickname: {}, phone: {}", new Object[] { userId, nickname, phone });
      return user;
    } catch (Exception e) {
      log.error("更新用户信息失败，userId: {}", userId, e);
      throw new RuntimeException("更新用户信息失败: " + e.getMessage());
    } 
  }
  
  private String saveBase64Avatar(String avatarBase64, Long userId) {
    try {
      String uploadDir = getUploadDirectory();
      File dir = new File(uploadDir);
      if (!dir.exists()) {
        log.info("Base64头像目录不存在，尝试创建: {}", uploadDir);
        boolean created = dir.mkdirs();
        if (!created) {
          File parentDir = dir.getParentFile();
          log.error("Base64头像目录创建失败 - 目录: {}", uploadDir);
          log.error("父目录存在: {}", Boolean.valueOf((parentDir != null && parentDir.exists())));
          log.error("父目录可写: {}", Boolean.valueOf((parentDir != null && parentDir.canWrite())));
          throw new RuntimeException("无法创建上传目录: " + uploadDir + " (父目录存在: " + ((parentDir != null && parentDir
              .exists()) ? 1 : 0) + ", 父目录可写: " + ((parentDir != null && parentDir
              .canWrite()) ? 1 : 0) + ")");
        } 
        log.info("Base64头像目录创建成功: {}", uploadDir);
      } 
      String base64Data = avatarBase64;
      if (avatarBase64.contains(","))
        base64Data = avatarBase64.split(",")[1]; 
      byte[] imageBytes = Base64.getDecoder().decode(base64Data);
      String filename = "avatar_" + userId + "_" + System.currentTimeMillis() + ".jpg";
      Path filePath = Paths.get(uploadDir, new String[] { filename });
      User existingUser = (User)this.userMapper.selectById(userId);
      if (existingUser != null)
        deleteOldAvatar(existingUser.getAvatarUrl(), uploadDir); 
      Files.write(filePath, imageBytes, new java.nio.file.OpenOption[0]);
      String relativePath = "/image/user-image/" + filename;
      String fullAvatarUrl = this.imageUrlPrefix + this.imageUrlPrefix;
      log.info("Base64头像保存成功，userId: {}, avatarUrl: {}", userId, fullAvatarUrl);
      return fullAvatarUrl;
    } catch (Exception e) {
      log.error("保存Base64头像失败，userId: {}", userId, e);
      throw new RuntimeException("保存头像失败: " + e.getMessage());
    } 
  }
  
  private String getUploadDirectory() {
    String jarPath = System.getProperty("user.dir");
    String uploadDir = jarPath + "/image/user-image";
    log.info("JAR文件路径: {}", jarPath);
    log.info("上传目录路径: {}", uploadDir);
    File dir = new File(uploadDir);
    log.info("目录是否存在: {}", Boolean.valueOf(dir.exists()));
    log.info("目录是否可写: {}", Boolean.valueOf(dir.canWrite()));
    log.info("目录是否可读: {}", Boolean.valueOf(dir.canRead()));
    return uploadDir;
  }
  
  private void deleteOldAvatar(String avatarUrl, String uploadDir) {
    if (avatarUrl != null && avatarUrl.contains("/image/user-image/"))
      try {
        String oldFilename = avatarUrl.substring(avatarUrl.lastIndexOf("/") + 1);
        File oldFile = new File(uploadDir, oldFilename);
        if (oldFile.exists()) {
          boolean deleted = oldFile.delete();
          if (deleted) {
            log.info("删除旧头像文件成功: {}", oldFilename);
          } else {
            log.warn("删除旧头像文件失败: {}", oldFilename);
          } 
        } 
      } catch (Exception e) {
        log.warn("删除旧头像文件时出错: {}", e.getMessage());
      }  
  }
}
