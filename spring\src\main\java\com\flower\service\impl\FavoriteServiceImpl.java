package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.Flower;
import com.flower.entity.UserFavorite;
import com.flower.mapper.FlowerMapper;
import com.flower.mapper.UserFavoriteMapper;
import com.flower.mapper.UserMapper;
import com.flower.service.FavoriteService;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FavoriteServiceImpl implements FavoriteService {
    @Autowired
    private UserFavoriteMapper userFavoriteMapper;

    @Autowired
    private FlowerMapper flowerMapper;

    @Autowired
    private UserMapper userMapper;

    public UserFavorite addToFavorites(Long userId, Long flowerId) {
        try {
            if (userId == null || flowerId == null)
                throw new IllegalArgumentException("用户ID和花卉ID不能为空");
            if (this.userMapper.selectById(userId) == null)
                throw new IllegalArgumentException("用户不存在，用户ID: " + userId);
            if (this.flowerMapper.selectById(flowerId) == null)
                throw new IllegalArgumentException("花卉不存在，花卉ID: " + flowerId);
            if (isFavorite(userId, flowerId).booleanValue())
                return null;
            UserFavorite favorite = new UserFavorite();
            favorite.setUserId(userId);
            favorite.setFlowerId(flowerId);
            favorite.setCreatedAt(LocalDateTime.now());
            int result = this.userFavoriteMapper.insert(favorite);
            if (result > 0)
                return favorite;
            throw new RuntimeException("插入收藏记录失败");
        } catch (Exception e) {
            throw new RuntimeException("添加收藏失败: " + e.getMessage(), e);
        }
    }

    public Boolean removeFromFavorites(Long userId, Long flowerId) {
        try {
            if (userId == null || flowerId == null)
                throw new IllegalArgumentException("用户ID和花卉ID不能为空");
            LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper();
            wrapper.eq(UserFavorite::getUserId, userId);
            wrapper.eq(UserFavorite::getFlowerId, flowerId);
            int result = this.userFavoriteMapper.delete((Wrapper)wrapper);
            return Boolean.valueOf((result > 0));
        } catch (Exception e) {
            throw new RuntimeException("取消收藏失败: " + e.getMessage(), e);
        }
    }

    public Boolean isFavorite(Long userId, Long flowerId) {
        try {
            if (userId == null || flowerId == null)
                return Boolean.valueOf(false);
            LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper();
            wrapper.eq(UserFavorite::getUserId, userId);
            wrapper.eq(UserFavorite::getFlowerId, flowerId);
            UserFavorite favorite = (UserFavorite)this.userFavoriteMapper.selectOne((Wrapper)wrapper);
            return Boolean.valueOf((favorite != null));
        } catch (Exception e) {
            return Boolean.valueOf(false);
        }
    }

    public Boolean toggleFavorite(Long userId, Long flowerId) {
        if (isFavorite(userId, flowerId).booleanValue()) {
            removeFromFavorites(userId, flowerId);
            return Boolean.valueOf(false);
        }
        addToFavorites(userId, flowerId);
        return Boolean.valueOf(true);
    }

    public List<UserFavorite> getUserFavorites(Long userId) {
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper();
        wrapper.eq(UserFavorite::getUserId, userId);
        wrapper.orderByDesc(UserFavorite::getCreatedAt);
        return this.userFavoriteMapper.selectList((Wrapper)wrapper);
    }

    public List<Flower> getUserFavoriteFlowers(Long userId) {
        List<UserFavorite> favorites = getUserFavorites(userId);
        if (favorites.isEmpty())
            return new ArrayList<>();
        List<Long> flowerIds = (List<Long>)favorites.stream().map(UserFavorite::getFlowerId).collect(Collectors.toList());
        LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper();
        wrapper.in(Flower::getId, flowerIds);
        wrapper.eq(Flower::getStatus, Integer.valueOf(1));
        List<Flower> flowers = this.flowerMapper.selectList((Wrapper)wrapper);
        return (List<Flower>)flowers.stream()
                .sorted((f1, f2) -> {
                    UserFavorite fav1 = favorites.stream().filter(f -> f.getFlowerId().equals(f1.getId())).findFirst().orElse(null);
                    UserFavorite fav2 = favorites.stream().filter(f -> f.getFlowerId().equals(f2.getId())).findFirst().orElse(null);
                    return (fav1 == null || fav2 == null) ? 0 : fav2.getCreatedAt().compareTo(fav1.getCreatedAt());
                }).collect(Collectors.toList());
    }

    public Integer getFavoriteCount(Long userId) {
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper();
        wrapper.eq(UserFavorite::getUserId, userId);
        return Integer.valueOf(Math.toIntExact(this.userFavoriteMapper.selectCount((Wrapper)wrapper).longValue()));
    }
}
