package com.flower.common;

public class Result<T> {
  private Integer code;
  
  private String message;
  
  private T data;
  
  private Long timestamp;
  
  public void setCode(Integer code) {
    this.code = code;
  }
  
  public void setMessage(String message) {
    this.message = message;
  }
  
  public void setData(T data) {
    this.data = data;
  }
  
  public void setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.common.Result))
      return false; 
    com.flower.common.Result<?> other = (com.flower.common.Result)o;
    if (!other.canEqual(this))
      return false; 
    Object this$code = getCode(), other$code = other.getCode();
    if ((this$code == null) ? (other$code != null) : !this$code.equals(other$code))
      return false; 
    Object this$timestamp = getTimestamp(), other$timestamp = other.getTimestamp();
    if ((this$timestamp == null) ? (other$timestamp != null) : !this$timestamp.equals(other$timestamp))
      return false; 
    Object this$message = getMessage(), other$message = other.getMessage();
    if ((this$message == null) ? (other$message != null) : !this$message.equals(other$message))
      return false; 
    Object this$data = getData(), other$data = other.getData();
    return !((this$data == null) ? (other$data != null) : !this$data.equals(other$data));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.common.Result;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $code = getCode();
    result = result * 59 + (($code == null) ? 43 : $code.hashCode());
    Object $timestamp = getTimestamp();
    result = result * 59 + (($timestamp == null) ? 43 : $timestamp.hashCode());
    Object $message = getMessage();
    result = result * 59 + (($message == null) ? 43 : $message.hashCode());
    Object $data = getData();
    return result * 59 + (($data == null) ? 43 : $data.hashCode());
  }
  
  public String toString() {
    return "Result(code=" + getCode() + ", message=" + getMessage() + ", data=" + getData() + ", timestamp=" + getTimestamp() + ")";
  }
  
  public Integer getCode() {
    return this.code;
  }
  
  public String getMessage() {
    return this.message;
  }
  
  public T getData() {
    return this.data;
  }
  
  public Long getTimestamp() {
    return this.timestamp;
  }
  
  public Result() {
    this.timestamp = Long.valueOf(System.currentTimeMillis());
  }
  
  public Result(Integer code, String message, T data) {
    this.code = code;
    this.message = message;
    this.data = data;
    this.timestamp = Long.valueOf(System.currentTimeMillis());
  }
  
  public static <T> com.flower.common.Result<T> success() {
    return new com.flower.common.Result<>(Integer.valueOf(200), "success", null);
  }
  
  public static <T> com.flower.common.Result<T> success(T data) {
    return new com.flower.common.Result<>(Integer.valueOf(200), "success", data);
  }
  
  public static <T> com.flower.common.Result<T> success(String message, T data) {
    return new com.flower.common.Result<>(Integer.valueOf(200), message, data);
  }
  
  public static <T> com.flower.common.Result<T> error(String message) {
    return new com.flower.common.Result<>(Integer.valueOf(500), message, null);
  }
  
  public static <T> com.flower.common.Result<T> error(Integer code, String message) {
    return new com.flower.common.Result<>(code, message, null);
  }
  
  public static <T> com.flower.common.Result<T> paramError(String message) {
    return new com.flower.common.Result<>(Integer.valueOf(400), message, null);
  }
  
  public static <T> com.flower.common.Result<T> unauthorized(String message) {
    return new com.flower.common.Result<>(Integer.valueOf(401), message, null);
  }
  
  public static <T> com.flower.common.Result<T> notFound(String message) {
    return new com.flower.common.Result<>(Integer.valueOf(404), message, null);
  }
}
