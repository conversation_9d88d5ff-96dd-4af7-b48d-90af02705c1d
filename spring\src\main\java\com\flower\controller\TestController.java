package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.CartItem;
import com.flower.entity.Flower;
import com.flower.entity.User;
import com.flower.entity.UserFavorite;
import com.flower.mapper.CartItemMapper;
import com.flower.mapper.FlowerMapper;
import com.flower.mapper.UserFavoriteMapper;
import com.flower.mapper.UserMapper;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/test"})
@CrossOrigin(origins = {"*"})
public class TestController {
  private static final Logger log = LoggerFactory.getLogger(com.flower.controller.TestController.class);
  
  @Autowired
  private UserMapper userMapper;
  
  @Autowired
  private FlowerMapper flowerMapper;
  
  @Autowired
  private UserFavoriteMapper userFavoriteMapper;
  
  @Autowired
  private CartItemMapper cartItemMapper;
  
  @GetMapping({"/db"})
  public Result<Map<String, Object>> testDatabase() {
    try {
      Map<String, Object> result = new HashMap<>();
      long userCount = this.userMapper.selectCount(null).longValue();
      result.put("userCount", Long.valueOf(userCount));
      long flowerCount = this.flowerMapper.selectCount(null).longValue();
      result.put("flowerCount", Long.valueOf(flowerCount));
      long favoriteCount = this.userFavoriteMapper.selectCount(null).longValue();
      result.put("favoriteCount", Long.valueOf(favoriteCount));
      long cartCount = this.cartItemMapper.selectCount(null).longValue();
      result.put("cartCount", Long.valueOf(cartCount));
      result.put("message", "数据库连接正常");
      return Result.success("数据库测试成功", result);
    } catch (Exception e) {
      log.error("数据库测试失败", e);
      return Result.error("数据库测试失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/users"})
  public Result<List<User>> testUsers() {
    try {
      List<User> users = this.userMapper.selectList(null);
      return Result.success("用户查询成功", users);
    } catch (Exception e) {
      log.error("用户查询失败", e);
      return Result.error("用户查询失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/flowers"})
  public Result<List<Flower>> testFlowers() {
    try {
      List<Flower> flowers = this.flowerMapper.selectList(null);
      return Result.success("花卉查询成功", flowers);
    } catch (Exception e) {
      log.error("花卉查询失败", e);
      return Result.error("花卉查询失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/favorite"})
  public Result<String> testFavorite(@RequestParam Long userId, @RequestParam Long flowerId) {
    try {
      User user = (User)this.userMapper.selectById(userId);
      if (user == null)
        return Result.error("用户不存在"); 
      Flower flower = (Flower)this.flowerMapper.selectById(flowerId);
      if (flower == null)
        return Result.error("花卉不存在"); 
      UserFavorite favorite = new UserFavorite();
      favorite.setUserId(userId);
      favorite.setFlowerId(flowerId);
      favorite.setCreatedAt(LocalDateTime.now());
      int result = this.userFavoriteMapper.insert(favorite);
      if (result > 0)
        return Result.success("收藏测试成功"); 
      return Result.error("收藏插入失败");
    } catch (Exception e) {
      log.error("收藏测试失败", e);
      return Result.error("收藏测试失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/cart"})
  public Result<String> testCart(@RequestParam Long userId, @RequestParam Long flowerId, @RequestParam Integer quantity) {
    try {
      User user = (User)this.userMapper.selectById(userId);
      if (user == null)
        return Result.error("用户不存在"); 
      Flower flower = (Flower)this.flowerMapper.selectById(flowerId);
      if (flower == null)
        return Result.error("花卉不存在"); 
      CartItem cartItem = new CartItem();
      cartItem.setUserId(userId);
      cartItem.setFlowerId(flowerId);
      cartItem.setQuantity(quantity);
      cartItem.setCreatedAt(LocalDateTime.now());
      cartItem.setUpdatedAt(LocalDateTime.now());
      int result = this.cartItemMapper.insert(cartItem);
      if (result > 0)
        return Result.success("购物车测试成功"); 
      return Result.error("购物车插入失败");
    } catch (Exception e) {
      log.error("购物车测试失败", e);
      return Result.error("购物车测试失败: " + e.getMessage());
    } 
  }
}
