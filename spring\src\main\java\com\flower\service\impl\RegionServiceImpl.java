package com.flower.service.impl;

import com.flower.entity.City;
import com.flower.entity.District;
import com.flower.entity.Province;
import com.flower.mapper.RegionMapper;
import com.flower.service.RegionService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
public class RegionServiceImpl implements RegionService {
  @Autowired
  private RegionMapper regionMapper;
  
  @Cacheable(value = {"provinces"}, key = "'all'")
  public List<Province> getAllProvinces() {
    return this.regionMapper.getAllProvinces();
  }
  
  @Cacheable(value = {"cities"}, key = "#provinceCode")
  public List<City> getCitiesByProvinceCode(String provinceCode) {
    return this.regionMapper.getCitiesByProvinceCode(provinceCode);
  }
  
  @Cacheable(value = {"districts"}, key = "#cityCode")
  public List<District> getDistrictsByCityCode(String cityCode) {
    return this.regionMapper.getDistrictsByCityCode(cityCode);
  }
  
  @Cacheable(value = {"regionData"}, key = "'complete'")
  public Map<String, Object> getRegionData() {
    Map<String, Object> result = new HashMap<>();
    List<Province> provinces = getAllProvinces();
    List<String> provinceNames = (List<String>)provinces.stream().map(Province::getName).collect(Collectors.toList());
    if (provinceNames.contains("新疆维吾尔自治区")) {
      provinceNames.remove("新疆维吾尔自治区");
      provinceNames.add(0, "新疆维吾尔自治区");
    } 
    Map<String, List<String>> cities = new HashMap<>();
    Map<String, List<String>> districts = new HashMap<>();
    for (Province province : provinces) {
      List<City> cityList = getCitiesByProvinceCode(province.getCode());
      List<String> cityNames = (List<String>)cityList.stream().map(City::getName).collect(Collectors.toList());
      cities.put(province.getName(), cityNames);
      for (City city : cityList) {
        List<District> districtList = getDistrictsByCityCode(city.getCode());
        List<String> districtNames = (List<String>)districtList.stream().map(District::getName).collect(Collectors.toList());
        districts.put(city.getName(), districtNames);
      } 
    } 
    result.put("provinces", provinceNames);
    result.put("cities", cities);
    result.put("districts", districts);
    return result;
  }
  
  @CacheEvict(value = {"provinces", "cities", "districts", "regionData"}, allEntries = true)
  public void clearAllRegionCache() {}
  
  public Map<String, String> getRegionCodes(String provinceName, String cityName, String districtName) {
    Map<String, String> codes = new HashMap<>();
    Province province = this.regionMapper.getProvinceByName(provinceName);
    if (province != null) {
      codes.put("provinceCode", province.getCode());
      City city = this.regionMapper.getCityByNameAndProvinceCode(cityName, province.getCode());
      if (city != null) {
        codes.put("cityCode", city.getCode());
        District district = this.regionMapper.getDistrictByNameAndCityCode(districtName, city.getCode());
        if (district != null)
          codes.put("districtCode", district.getCode()); 
      } 
    } 
    return codes;
  }
}
