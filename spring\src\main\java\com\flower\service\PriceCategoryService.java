package com.flower.service;

import com.flower.common.PageResult;
import com.flower.entity.PriceCategory;
import java.math.BigDecimal;
import java.util.List;

public interface PriceCategoryService {
  PageResult<PriceCategory> getPriceCategoriesByPage(Long paramLong1, Long paramLong2, String paramString, Integer paramInteger);
  
  PriceCategory getPriceCategoryById(Long paramLong);
  
  boolean createPriceCategory(PriceCategory paramPriceCategory);
  
  boolean updatePriceCategory(PriceCategory paramPriceCategory);
  
  boolean deletePriceCategory(Long paramLong);
  
  boolean updatePriceCategoryStatus(Long paramLong, Integer paramInteger);
  
  List<PriceCategory> getAllActivePriceCategories();
  
  PriceCategory getPriceCategoryByPrice(BigDecimal paramBigDecimal);
}
