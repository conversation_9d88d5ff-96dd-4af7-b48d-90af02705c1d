package com.flower.websocket;

public class NotificationMessage {
  private String type;
  private Object data;
  private Long timestamp;

  public String getType() {
    return this.type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public Object getData() {
    return this.data;
  }

  public void setData(Object data) {
    this.data = data;
  }

  public Long getTimestamp() {
    return this.timestamp;
  }

  public void setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
  }
}

