package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

@TableName("user_addresses")
public class UserAddress {
  @TableId(type = IdType.AUTO)
  private Long id;
  
  private Long userId;
  
  private String recipientName;
  
  private String recipientPhone;
  
  private String province;
  
  private String city;
  
  private String district;
  
  private String detailedAddress;
  
  private String postalCode;
  
  private Integer isDefault;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setUserId(Long userId) {
    this.userId = userId;
  }
  
  public void setRecipientName(String recipientName) {
    this.recipientName = recipientName;
  }
  
  public void setRecipientPhone(String recipientPhone) {
    this.recipientPhone = recipientPhone;
  }
  
  public void setProvince(String province) {
    this.province = province;
  }
  
  public void setCity(String city) {
    this.city = city;
  }
  
  public void setDistrict(String district) {
    this.district = district;
  }
  
  public void setDetailedAddress(String detailedAddress) {
    this.detailedAddress = detailedAddress;
  }
  
  public void setPostalCode(String postalCode) {
    this.postalCode = postalCode;
  }
  
  public void setIsDefault(Integer isDefault) {
    this.isDefault = isDefault;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.UserAddress))
      return false; 
    com.flower.entity.UserAddress other = (com.flower.entity.UserAddress)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$userId = getUserId(), other$userId = other.getUserId();
    if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId))
      return false; 
    Object this$isDefault = getIsDefault(), other$isDefault = other.getIsDefault();
    if ((this$isDefault == null) ? (other$isDefault != null) : !this$isDefault.equals(other$isDefault))
      return false; 
    Object this$recipientName = getRecipientName(), other$recipientName = other.getRecipientName();
    if ((this$recipientName == null) ? (other$recipientName != null) : !this$recipientName.equals(other$recipientName))
      return false; 
    Object this$recipientPhone = getRecipientPhone(), other$recipientPhone = other.getRecipientPhone();
    if ((this$recipientPhone == null) ? (other$recipientPhone != null) : !this$recipientPhone.equals(other$recipientPhone))
      return false; 
    Object this$province = getProvince(), other$province = other.getProvince();
    if ((this$province == null) ? (other$province != null) : !this$province.equals(other$province))
      return false; 
    Object this$city = getCity(), other$city = other.getCity();
    if ((this$city == null) ? (other$city != null) : !this$city.equals(other$city))
      return false; 
    Object this$district = getDistrict(), other$district = other.getDistrict();
    if ((this$district == null) ? (other$district != null) : !this$district.equals(other$district))
      return false; 
    Object this$detailedAddress = getDetailedAddress(), other$detailedAddress = other.getDetailedAddress();
    if ((this$detailedAddress == null) ? (other$detailedAddress != null) : !this$detailedAddress.equals(other$detailedAddress))
      return false; 
    Object this$postalCode = getPostalCode(), other$postalCode = other.getPostalCode();
    if ((this$postalCode == null) ? (other$postalCode != null) : !this$postalCode.equals(other$postalCode))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.UserAddress;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $userId = getUserId();
    result = result * 59 + (($userId == null) ? 43 : $userId.hashCode());
    Object $isDefault = getIsDefault();
    result = result * 59 + (($isDefault == null) ? 43 : $isDefault.hashCode());
    Object $recipientName = getRecipientName();
    result = result * 59 + (($recipientName == null) ? 43 : $recipientName.hashCode());
    Object $recipientPhone = getRecipientPhone();
    result = result * 59 + (($recipientPhone == null) ? 43 : $recipientPhone.hashCode());
    Object $province = getProvince();
    result = result * 59 + (($province == null) ? 43 : $province.hashCode());
    Object $city = getCity();
    result = result * 59 + (($city == null) ? 43 : $city.hashCode());
    Object $district = getDistrict();
    result = result * 59 + (($district == null) ? 43 : $district.hashCode());
    Object $detailedAddress = getDetailedAddress();
    result = result * 59 + (($detailedAddress == null) ? 43 : $detailedAddress.hashCode());
    Object $postalCode = getPostalCode();
    result = result * 59 + (($postalCode == null) ? 43 : $postalCode.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public String toString() {
    return "UserAddress(id=" + getId() + ", userId=" + getUserId() + ", recipientName=" + getRecipientName() + ", recipientPhone=" + getRecipientPhone() + ", province=" + getProvince() + ", city=" + getCity() + ", district=" + getDistrict() + ", detailedAddress=" + getDetailedAddress() + ", postalCode=" + getPostalCode() + ", isDefault=" + getIsDefault() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public Long getId() {
    return this.id;
  }
  
  public Long getUserId() {
    return this.userId;
  }
  
  public String getRecipientName() {
    return this.recipientName;
  }
  
  public String getRecipientPhone() {
    return this.recipientPhone;
  }
  
  public String getProvince() {
    return this.province;
  }
  
  public String getCity() {
    return this.city;
  }
  
  public String getDistrict() {
    return this.district;
  }
  
  public String getDetailedAddress() {
    return this.detailedAddress;
  }
  
  public String getPostalCode() {
    return this.postalCode;
  }
  
  public Integer getIsDefault() {
    return this.isDefault;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
