/* search.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  min-height: 100vh;
}

/* 搜索头部 */
.search-header {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e8e8e8;
}

.search-input-container {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 15rpx 25rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.cancel-btn {
  color: #FF69B4;
  font-size: 28rpx;
  margin-left: 20rpx;
}

/* 搜索历史 */
.search-history {
  background-color: #fff;
  margin-top: 20rpx;
  padding: 30rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.history-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.clear-btn {
  font-size: 24rpx;
  color: #999;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.history-tag {
  background-color: #f5f5f5;
  color: #666;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

/* 热门搜索 */
.hot-search {
  background-color: #fff;
  margin-top: 20rpx;
  padding: 30rpx;
}

.hot-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 25rpx;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.hot-tag {
  background-color: #fff5f8;
  color: #FF69B4;
  border: 1rpx solid #FFB6C1;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

/* 搜索结果 */
.search-results {
  background-color: #fff;
  margin-top: 20rpx;
}

.result-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #e8e8e8;
}

.result-count {
  font-size: 24rpx;
  color: #999;
}

.product-list {
  padding: 0 30rpx;
}

.product-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 15rpx;
  margin-right: 25rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: bold;
}

.price-value {
  font-size: 32rpx;
  color: #FF69B4;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
