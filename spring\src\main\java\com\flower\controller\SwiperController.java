package com.flower.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.Result;
import com.flower.entity.Swiper;
import com.flower.service.SwiperService;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping({"/swiper"})
@CrossOrigin
public class SwiperController {
  @Autowired
  private SwiperService swiperService;
  
  @GetMapping({"/page"})
  public Result<Page<Swiper>> getPage(@RequestParam(defaultValue = "1") int pageNum, @RequestParam(defaultValue = "10") int pageSize, @RequestParam(required = false) String title, @RequestParam(required = false) Integer status) {
    Page<Swiper> page = this.swiperService.getPage(pageNum, pageSize, title, status);
    return Result.success(page);
  }
  
  @GetMapping({"/active"})
  public Result<List<Swiper>> getActiveSwipers() {
    List<Swiper> swipers = this.swiperService.getActiveSwipers();
    return Result.success(swipers);
  }
  
  @GetMapping({"/{id}"})
  public Result<Swiper> getById(@PathVariable Integer id) {
    Swiper swiper = (Swiper)this.swiperService.getById(id);
    return Result.success(swiper);
  }
  
  @PostMapping
  public Result<String> save(@RequestBody Swiper swiper) {
    swiper.setCreateTime(LocalDateTime.now());
    swiper.setUpdateTime(LocalDateTime.now());
    if (swiper.getStatus() == null)
      swiper.setStatus(Integer.valueOf(1)); 
    if (swiper.getSortOrder() == null)
      swiper.setSortOrder(Integer.valueOf(0)); 
    boolean success = this.swiperService.save(swiper);
    return success ? Result.success("新增成功") : Result.error("新增失败");
  }
  
  @PutMapping
  public Result<String> update(@RequestBody Swiper swiper) {
    swiper.setUpdateTime(LocalDateTime.now());
    boolean success = this.swiperService.updateById(swiper);
    return success ? Result.success("更新成功") : Result.error("更新失败");
  }
  
  @DeleteMapping({"/{id}"})
  public Result<String> delete(@PathVariable Integer id) {
    boolean success = this.swiperService.removeById(id);
    return success ? Result.success("删除成功") : Result.error("删除失败");
  }
  
  @DeleteMapping({"/batch"})
  public Result<String> deleteBatch(@RequestBody List<Integer> ids) {
    boolean success = this.swiperService.removeByIds(ids);
    return success ? Result.success("批量删除成功") : Result.error("批量删除失败");
  }
  
  @PostMapping({"/upload"})
  public Result<String> uploadImage(@RequestParam("file") MultipartFile file) {
    try {
      String imageUrl = this.swiperService.uploadImage(file);
      return Result.success(imageUrl);
    } catch (Exception e) {
      return Result.error("图片上传失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/status"})
  public Result<String> updateStatus(@RequestParam Integer id, @RequestParam Integer status) {
    boolean success = this.swiperService.updateStatus(id, status);
    return success ? Result.success("状态更新成功") : Result.error("状态更新失败");
  }
  
  @PutMapping({"/sort"})
  public Result<String> updateSortOrder(@RequestParam Integer id, @RequestParam Integer sortOrder) {
    boolean success = this.swiperService.updateSortOrder(id, sortOrder);
    return success ? Result.success("排序更新成功") : Result.error("排序更新失败");
  }
}
