package com.flower.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flower.entity.City;
import com.flower.entity.District;
import com.flower.entity.Province;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface RegionMapper extends BaseMapper<Province> {
  @Select({"SELECT * FROM provinces ORDER BY CASE WHEN name = '新疆维吾尔自治区' THEN 0 ELSE 1 END, code"})
  List<Province> getAllProvinces();
  
  @Select({"SELECT * FROM cities WHERE province_code = #{provinceCode} ORDER BY code"})
  List<City> getCitiesByProvinceCode(String paramString);
  
  @Select({"SELECT * FROM districts WHERE city_code = #{cityCode} ORDER BY code"})
  List<District> getDistrictsByCityCode(String paramString);
  
  @Select({"SELECT * FROM provinces WHERE name = #{provinceName}"})
  Province getProvinceByName(String paramString);
  
  @Select({"SELECT * FROM cities WHERE name = #{cityName} AND province_code = #{provinceCode}"})
  City getCityByNameAndProvinceCode(String paramString1, String paramString2);
  
  @Select({"SELECT * FROM districts WHERE name = #{districtName} AND city_code = #{cityCode}"})
  District getDistrictByNameAndCityCode(String paramString1, String paramString2);
  
  @Insert({"INSERT INTO provinces (code, name, created_at, updated_at) VALUES (#{code}, #{name}, NOW(), NOW())"})
  @Options(useGeneratedKeys = true, keyProperty = "id")
  int insertProvince(Province paramProvince);
  
  @Update({"UPDATE provinces SET name = #{name}, updated_at = NOW() WHERE id = #{id}"})
  int updateProvince(Province paramProvince);
  
  @Delete({"DELETE FROM provinces WHERE id = #{id}"})
  int deleteProvince(Integer paramInteger);
  
  @Insert({"INSERT INTO cities (code, name, province_code, created_at, updated_at) VALUES (#{code}, #{name}, #{provinceCode}, NOW(), NOW())"})
  @Options(useGeneratedKeys = true, keyProperty = "id")
  int insertCity(City paramCity);
  
  @Update({"UPDATE cities SET name = #{name}, updated_at = NOW() WHERE id = #{id}"})
  int updateCity(City paramCity);
  
  @Delete({"DELETE FROM cities WHERE id = #{id}"})
  int deleteCity(Integer paramInteger);
  
  @Insert({"INSERT INTO districts (code, name, city_code, created_at, updated_at) VALUES (#{code}, #{name}, #{cityCode}, NOW(), NOW())"})
  @Options(useGeneratedKeys = true, keyProperty = "id")
  int insertDistrict(District paramDistrict);
  
  @Update({"UPDATE districts SET name = #{name}, updated_at = NOW() WHERE id = #{id}"})
  int updateDistrict(District paramDistrict);
  
  @Delete({"DELETE FROM districts WHERE id = #{id}"})
  int deleteDistrict(Integer paramInteger);
}
