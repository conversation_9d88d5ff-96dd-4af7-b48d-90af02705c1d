package com.flower.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@ServerEndpoint("/ws/notifications")
public class NotificationWebSocket {
  private static final Logger log = LoggerFactory.getLogger(com.flower.websocket.NotificationWebSocket.class);
  
  private static final AtomicInteger onlineCount = new AtomicInteger(0);
  
  private static final ConcurrentHashMap<String, com.flower.websocket.NotificationWebSocket> webSocketMap = new ConcurrentHashMap<>();
  
  private Session session;
  
  private String userId = "";
  
  private static final ObjectMapper objectMapper = new ObjectMapper();
  
  @OnOpen
  public void onOpen(Session session) {
    this.session = session;
    this.userId = session.getId();
    if (webSocketMap.containsKey(this.userId)) {
      webSocketMap.remove(this.userId);
      webSocketMap.put(this.userId, this);
    } else {
      webSocketMap.put(this.userId, this);
      addOnlineCount();
    } 
    log.info("用户连接:{}, 当前在线人数为:{}", this.userId, Integer.valueOf(getOnlineCount()));
    try {
      sendMessage("连接成功");
    } catch (IOException e) {
      log.error("用户:{}, 网络异常!!!!!!", this.userId);
    } 
  }
  
  @OnClose
  public void onClose() {
    if (webSocketMap.containsKey(this.userId)) {
      webSocketMap.remove(this.userId);
      subOnlineCount();
    } 
    log.info("用户退出:{}, 当前在线人数为:{}", this.userId, Integer.valueOf(getOnlineCount()));
  }
  
  @OnMessage
  public void onMessage(String message, Session session) {
    log.info("用户消息:{}, 报文:{}", this.userId, message);
    if ("ping".equals(message))
      try {
        sendMessage("pong");
      } catch (IOException e) {
        log.error("发送心跳响应失败", e);
      }  
  }
  
  @OnError
  public void onError(Session session, Throwable error) {
    log.error("用户错误:{}, 原因:{}", this.userId, error.getMessage());
    error.printStackTrace();
  }
  
  public void sendMessage(String message) throws IOException {
    this.session.getBasicRemote().sendText(message);
  }
  
  public static void sendInfo(String message, String userId) throws IOException {
    log.info("发送消息到:{}, 报文:{}", userId, message);
    if (userId != null && webSocketMap.containsKey(userId)) {
      ((com.flower.websocket.NotificationWebSocket)webSocketMap.get(userId)).sendMessage(message);
    } else {
      log.error("用户:{}, 不在线！", userId);
    } 
  }
  
  public static void sendToAll(String message) {
    log.info("群发消息:{}", message);
    for (com.flower.websocket.NotificationWebSocket item : webSocketMap.values()) {
      try {
        item.sendMessage(message);
      } catch (IOException e) {
        log.error("群发消息失败", e);
      } 
    } 
  }
  
  public static void sendNewOrderNotification(Object orderData) {
    try {
      NotificationMessage message = new NotificationMessage();
      message.setType("NEW_ORDER");
      message.setData(orderData);
      message.setTimestamp(Long.valueOf(System.currentTimeMillis()));
      String jsonMessage = objectMapper.writeValueAsString(message);
      sendToAll(jsonMessage);
      log.info("发送新订单通知: {}", jsonMessage);
    } catch (Exception e) {
      log.error("发送新订单通知失败", e);
    } 
  }
  
  public static void sendOrderStatusChangeNotification(Object orderData) {
    try {
      NotificationMessage message = new NotificationMessage();
      message.setType("ORDER_STATUS_CHANGE");
      message.setData(orderData);
      message.setTimestamp(Long.valueOf(System.currentTimeMillis()));
      String jsonMessage = objectMapper.writeValueAsString(message);
      sendToAll(jsonMessage);
      log.info("发送订单状态变更通知: {}", jsonMessage);
    } catch (Exception e) {
      log.error("发送订单状态变更通知失败", e);
    } 
  }
  
  public static void sendLowStockNotification(Object stockData) {
    try {
      NotificationMessage message = new NotificationMessage();
      message.setType("LOW_STOCK");
      message.setData(stockData);
      message.setTimestamp(Long.valueOf(System.currentTimeMillis()));
      String jsonMessage = objectMapper.writeValueAsString(message);
      sendToAll(jsonMessage);
      log.info("发送库存预警通知: {}", jsonMessage);
    } catch (Exception e) {
      log.error("发送库存预警通知失败", e);
    } 
  }
  
  public static synchronized int getOnlineCount() {
    return onlineCount.get();
  }
  
  public static synchronized void addOnlineCount() {
    onlineCount.incrementAndGet();
  }
  
  public static synchronized void subOnlineCount() {
    onlineCount.decrementAndGet();
  }
}
