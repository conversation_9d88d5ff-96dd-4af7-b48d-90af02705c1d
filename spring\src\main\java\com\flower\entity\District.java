package com.flower.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

@TableName("districts")
public class District {
  @TableId(type = IdType.AUTO)
  private Integer id;
  
  private String code;
  
  private String name;
  
  @TableField("city_code")
  private String cityCode;
  
  @TableField(value = "created_at", fill = FieldFill.INSERT)
  private LocalDateTime createdAt;
  
  @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
  private LocalDateTime updatedAt;
  
  public void setId(Integer id) {
    this.id = id;
  }
  
  public void setCode(String code) {
    this.code = code;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public void setCityCode(String cityCode) {
    this.cityCode = cityCode;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.District))
      return false; 
    com.flower.entity.District other = (com.flower.entity.District)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$code = getCode(), other$code = other.getCode();
    if ((this$code == null) ? (other$code != null) : !this$code.equals(other$code))
      return false; 
    Object this$name = getName(), other$name = other.getName();
    if ((this$name == null) ? (other$name != null) : !this$name.equals(other$name))
      return false; 
    Object this$cityCode = getCityCode(), other$cityCode = other.getCityCode();
    if ((this$cityCode == null) ? (other$cityCode != null) : !this$cityCode.equals(other$cityCode))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.District;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $code = getCode();
    result = result * 59 + (($code == null) ? 43 : $code.hashCode());
    Object $name = getName();
    result = result * 59 + (($name == null) ? 43 : $name.hashCode());
    Object $cityCode = getCityCode();
    result = result * 59 + (($cityCode == null) ? 43 : $cityCode.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public String toString() {
    return "District(id=" + getId() + ", code=" + getCode() + ", name=" + getName() + ", cityCode=" + getCityCode() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public Integer getId() {
    return this.id;
  }
  
  public String getCode() {
    return this.code;
  }
  
  public String getName() {
    return this.name;
  }
  
  public String getCityCode() {
    return this.cityCode;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
