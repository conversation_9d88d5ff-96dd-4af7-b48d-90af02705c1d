package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

@TableName("cart_items")
public class CartItem {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private Long userId;
  
  private Long flowerId;
  
  private Integer quantity;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setUserId(Long userId) {
    this.userId = userId;
  }
  
  public void setFlowerId(Long flowerId) {
    this.flowerId = flowerId;
  }
  
  public void setQuantity(Integer quantity) {
    this.quantity = quantity;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public String toString() {
    return "CartItem(id=" + getId() + ", userId=" + getUserId() + ", flowerId=" + getFlowerId() + ", quantity=" + getQuantity() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.CartItem))
      return false; 
    com.flower.entity.CartItem other = (com.flower.entity.CartItem)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$userId = getUserId(), other$userId = other.getUserId();
    if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId))
      return false; 
    Object this$flowerId = getFlowerId(), other$flowerId = other.getFlowerId();
    if ((this$flowerId == null) ? (other$flowerId != null) : !this$flowerId.equals(other$flowerId))
      return false; 
    Object this$quantity = getQuantity(), other$quantity = other.getQuantity();
    if ((this$quantity == null) ? (other$quantity != null) : !this$quantity.equals(other$quantity))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.CartItem;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $userId = getUserId();
    result = result * 59 + (($userId == null) ? 43 : $userId.hashCode());
    Object $flowerId = getFlowerId();
    result = result * 59 + (($flowerId == null) ? 43 : $flowerId.hashCode());
    Object $quantity = getQuantity();
    result = result * 59 + (($quantity == null) ? 43 : $quantity.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public Long getUserId() {
    return this.userId;
  }
  
  public Long getFlowerId() {
    return this.flowerId;
  }
  
  public Integer getQuantity() {
    return this.quantity;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
