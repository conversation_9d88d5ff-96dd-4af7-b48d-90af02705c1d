package com.flower.service;

import com.flower.common.PageResult;
import com.flower.entity.Category;
import com.flower.entity.Flower;
import com.flower.vo.FlowerVO;
import java.math.BigDecimal;
import java.util.List;

public interface FlowerService {
  List<Category> getAllCategories();
  
  PageResult<FlowerVO> getFlowersByPage(Long paramLong1, Long paramLong2, Long paramLong3, String paramString, BigDecimal paramBigDecimal1, BigDecimal paramBigDecimal2, Boolean paramBoolean);
  
  Flower getFlowerById(Long paramLong);
  
  List<Flower> getFeaturedFlowers(Integer paramInteger, Long paramLong, BigDecimal paramBigDecimal1, BigDecimal paramBigDecimal2);
  
  PageResult<FlowerVO> searchFlowers(String paramString, Long paramLong1, Long paramLong2);
  
  PageResult<FlowerVO> getFlowersByCategory(Long paramLong1, Long paramLong2, Long paramLong3);
  
  List<String> getHotKeywords();
}
