package com.flower.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.flower.entity.Swiper;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

public interface SwiperService extends IService<Swiper> {
  Page<Swiper> getPage(int paramInt1, int paramInt2, String paramString, Integer paramInteger);
  
  List<Swiper> getActiveSwipers();
  
  String uploadImage(MultipartFile paramMultipartFile);
  
  boolean updateStatus(Integer paramInteger1, Integer paramInteger2);
  
  boolean updateSortOrder(Integer paramInteger1, Integer paramInteger2);
}
