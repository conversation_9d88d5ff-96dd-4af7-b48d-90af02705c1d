package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

@TableName("admin_users")
public class AdminUser {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private String username;
  
  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  private String password;
  
  private String email;
  
  private String realName;
  
  private String role;
  
  private Integer status;
  
  private String avatar;
  
  private String phone;
  
  private LocalDateTime lastLoginTime;
  
  private String lastLoginIp;
  
  private String remark;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setUsername(String username) {
    this.username = username;
  }
  
  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  public void setPassword(String password) {
    this.password = password;
  }
  
  public void setEmail(String email) {
    this.email = email;
  }
  
  public void setRealName(String realName) {
    this.realName = realName;
  }
  
  public void setRole(String role) {
    this.role = role;
  }
  
  public void setStatus(Integer status) {
    this.status = status;
  }
  
  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }
  
  public void setPhone(String phone) {
    this.phone = phone;
  }
  
  public void setLastLoginTime(LocalDateTime lastLoginTime) {
    this.lastLoginTime = lastLoginTime;
  }
  
  public void setLastLoginIp(String lastLoginIp) {
    this.lastLoginIp = lastLoginIp;
  }
  
  public void setRemark(String remark) {
    this.remark = remark;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public String toString() {
    return "AdminUser(id=" + getId() + ", username=" + getUsername() + ", password=" + getPassword() + ", email=" + getEmail() + ", realName=" + getRealName() + ", role=" + getRole() + ", status=" + getStatus() + ", avatar=" + getAvatar() + ", phone=" + getPhone() + ", lastLoginTime=" + getLastLoginTime() + ", lastLoginIp=" + getLastLoginIp() + ", remark=" + getRemark() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.AdminUser))
      return false; 
    com.flower.entity.AdminUser other = (com.flower.entity.AdminUser)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$status = getStatus(), other$status = other.getStatus();
    if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status))
      return false; 
    Object this$username = getUsername(), other$username = other.getUsername();
    if ((this$username == null) ? (other$username != null) : !this$username.equals(other$username))
      return false; 
    Object this$password = getPassword(), other$password = other.getPassword();
    if ((this$password == null) ? (other$password != null) : !this$password.equals(other$password))
      return false; 
    Object this$email = getEmail(), other$email = other.getEmail();
    if ((this$email == null) ? (other$email != null) : !this$email.equals(other$email))
      return false; 
    Object this$realName = getRealName(), other$realName = other.getRealName();
    if ((this$realName == null) ? (other$realName != null) : !this$realName.equals(other$realName))
      return false; 
    Object this$role = getRole(), other$role = other.getRole();
    if ((this$role == null) ? (other$role != null) : !this$role.equals(other$role))
      return false; 
    Object this$avatar = getAvatar(), other$avatar = other.getAvatar();
    if ((this$avatar == null) ? (other$avatar != null) : !this$avatar.equals(other$avatar))
      return false; 
    Object this$phone = getPhone(), other$phone = other.getPhone();
    if ((this$phone == null) ? (other$phone != null) : !this$phone.equals(other$phone))
      return false; 
    Object this$lastLoginTime = getLastLoginTime(), other$lastLoginTime = other.getLastLoginTime();
    if ((this$lastLoginTime == null) ? (other$lastLoginTime != null) : !this$lastLoginTime.equals(other$lastLoginTime))
      return false; 
    Object this$lastLoginIp = getLastLoginIp(), other$lastLoginIp = other.getLastLoginIp();
    if ((this$lastLoginIp == null) ? (other$lastLoginIp != null) : !this$lastLoginIp.equals(other$lastLoginIp))
      return false; 
    Object this$remark = getRemark(), other$remark = other.getRemark();
    if ((this$remark == null) ? (other$remark != null) : !this$remark.equals(other$remark))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.AdminUser;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $status = getStatus();
    result = result * 59 + (($status == null) ? 43 : $status.hashCode());
    Object $username = getUsername();
    result = result * 59 + (($username == null) ? 43 : $username.hashCode());
    Object $password = getPassword();
    result = result * 59 + (($password == null) ? 43 : $password.hashCode());
    Object $email = getEmail();
    result = result * 59 + (($email == null) ? 43 : $email.hashCode());
    Object $realName = getRealName();
    result = result * 59 + (($realName == null) ? 43 : $realName.hashCode());
    Object $role = getRole();
    result = result * 59 + (($role == null) ? 43 : $role.hashCode());
    Object $avatar = getAvatar();
    result = result * 59 + (($avatar == null) ? 43 : $avatar.hashCode());
    Object $phone = getPhone();
    result = result * 59 + (($phone == null) ? 43 : $phone.hashCode());
    Object $lastLoginTime = getLastLoginTime();
    result = result * 59 + (($lastLoginTime == null) ? 43 : $lastLoginTime.hashCode());
    Object $lastLoginIp = getLastLoginIp();
    result = result * 59 + (($lastLoginIp == null) ? 43 : $lastLoginIp.hashCode());
    Object $remark = getRemark();
    result = result * 59 + (($remark == null) ? 43 : $remark.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public String getUsername() {
    return this.username;
  }
  
  public String getPassword() {
    return this.password;
  }
  
  public String getEmail() {
    return this.email;
  }
  
  public String getRealName() {
    return this.realName;
  }
  
  public String getRole() {
    return this.role;
  }
  
  public Integer getStatus() {
    return this.status;
  }
  
  public String getAvatar() {
    return this.avatar;
  }
  
  public String getPhone() {
    return this.phone;
  }
  
  public LocalDateTime getLastLoginTime() {
    return this.lastLoginTime;
  }
  
  public String getLastLoginIp() {
    return this.lastLoginIp;
  }
  
  public String getRemark() {
    return this.remark;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
