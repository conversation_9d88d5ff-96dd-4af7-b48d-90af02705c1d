// 常量配置文件

// 应用配置
const APP_CONFIG = {
  NAME: '恒通礼业',
  VERSION: '1.0.0'
}

// 分类图标映射
const CATEGORY_ICONS = {
  '年会礼品': '/images/category/annual.png',
  '商务礼品': '/images/category/business.png',
  '婚庆礼品': '/images/category/wedding.png',
  '纺织礼品': '/images/category/textile.png',
  '印刷礼品': '/images/category/print.png',
  '节日礼品': '/images/category/festival.png',
  '家电礼品': '/images/category/appliance.png',
  '高端礼品': '/images/category/luxury.png'
}

// 默认分类图标
const DEFAULT_CATEGORY_ICON = '/images/category/default.png'

// 商品状态
const PRODUCT_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0
}

// 订单状态
const ORDER_STATUS = {
  PENDING: 0,    // 待付款
  PAID: 1,       // 已付款
  SHIPPED: 2,    // 已发货
  DELIVERED: 3,  // 已送达
  CANCELLED: 4   // 已取消
}

// 分页配置
const PAGE_CONFIG = {
  DEFAULT_SIZE: 10,
  MAX_SIZE: 50
}

// 图片配置
const IMAGE_CONFIG = {
  DEFAULT_AVATAR: '/images/default-avatar.png',
  DEFAULT_PRODUCT: '/images/default-product.png',
  PLACEHOLDER: '/images/placeholder.png'
}

// 颜色配置
const COLORS = {
  PRIMARY: '#FF69B4',
  SECONDARY: '#FFB6C1',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#f5222d',
  TEXT_PRIMARY: '#333333',
  TEXT_SECONDARY: '#666666',
  TEXT_LIGHT: '#999999',
  BORDER: '#e8e8e8',
  BACKGROUND: '#f5f5f5'
}

module.exports = {
  APP_CONFIG,
  CATEGORY_ICONS,
  DEFAULT_CATEGORY_ICON,
  PRODUCT_STATUS,
  ORDER_STATUS,
  PAGE_CONFIG,
  IMAGE_CONFIG,
  COLORS
}
