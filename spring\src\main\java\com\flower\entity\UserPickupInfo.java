package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

@TableName("user_pickup_info")
public class UserPickupInfo {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private Long userId;
  
  private String pickupName;
  
  private String pickupPhone;
  
  private String pickupTime;
  
  private String remark;
  
  private String backupAddress;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setUserId(Long userId) {
    this.userId = userId;
  }
  
  public void setPickupName(String pickupName) {
    this.pickupName = pickupName;
  }
  
  public void setPickupPhone(String pickupPhone) {
    this.pickupPhone = pickupPhone;
  }
  
  public void setPickupTime(String pickupTime) {
    this.pickupTime = pickupTime;
  }
  
  public void setRemark(String remark) {
    this.remark = remark;
  }
  
  public void setBackupAddress(String backupAddress) {
    this.backupAddress = backupAddress;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public String toString() {
    return "UserPickupInfo(id=" + getId() + ", userId=" + getUserId() + ", pickupName=" + getPickupName() + ", pickupPhone=" + getPickupPhone() + ", pickupTime=" + getPickupTime() + ", remark=" + getRemark() + ", backupAddress=" + getBackupAddress() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.UserPickupInfo))
      return false; 
    com.flower.entity.UserPickupInfo other = (com.flower.entity.UserPickupInfo)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$userId = getUserId(), other$userId = other.getUserId();
    if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId))
      return false; 
    Object this$pickupName = getPickupName(), other$pickupName = other.getPickupName();
    if ((this$pickupName == null) ? (other$pickupName != null) : !this$pickupName.equals(other$pickupName))
      return false; 
    Object this$pickupPhone = getPickupPhone(), other$pickupPhone = other.getPickupPhone();
    if ((this$pickupPhone == null) ? (other$pickupPhone != null) : !this$pickupPhone.equals(other$pickupPhone))
      return false; 
    Object this$pickupTime = getPickupTime(), other$pickupTime = other.getPickupTime();
    if ((this$pickupTime == null) ? (other$pickupTime != null) : !this$pickupTime.equals(other$pickupTime))
      return false; 
    Object this$remark = getRemark(), other$remark = other.getRemark();
    if ((this$remark == null) ? (other$remark != null) : !this$remark.equals(other$remark))
      return false; 
    Object this$backupAddress = getBackupAddress(), other$backupAddress = other.getBackupAddress();
    if ((this$backupAddress == null) ? (other$backupAddress != null) : !this$backupAddress.equals(other$backupAddress))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.UserPickupInfo;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $userId = getUserId();
    result = result * 59 + (($userId == null) ? 43 : $userId.hashCode());
    Object $pickupName = getPickupName();
    result = result * 59 + (($pickupName == null) ? 43 : $pickupName.hashCode());
    Object $pickupPhone = getPickupPhone();
    result = result * 59 + (($pickupPhone == null) ? 43 : $pickupPhone.hashCode());
    Object $pickupTime = getPickupTime();
    result = result * 59 + (($pickupTime == null) ? 43 : $pickupTime.hashCode());
    Object $remark = getRemark();
    result = result * 59 + (($remark == null) ? 43 : $remark.hashCode());
    Object $backupAddress = getBackupAddress();
    result = result * 59 + (($backupAddress == null) ? 43 : $backupAddress.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public Long getUserId() {
    return this.userId;
  }
  
  public String getPickupName() {
    return this.pickupName;
  }
  
  public String getPickupPhone() {
    return this.pickupPhone;
  }
  
  public String getPickupTime() {
    return this.pickupTime;
  }
  
  public String getRemark() {
    return this.remark;
  }
  
  public String getBackupAddress() {
    return this.backupAddress;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
