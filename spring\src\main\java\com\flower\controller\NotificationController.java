package com.flower.controller;

import com.flower.common.Result;
import com.flower.websocket.NotificationWebSocket;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/api/notification"})
@CrossOrigin(origins = {"*"})
public class NotificationController {
  private static final Logger log = LoggerFactory.getLogger(com.flower.controller.NotificationController.class);
  
  @PostMapping({"/test/new-order"})
  public Result<String> sendTestNewOrderNotification() {
    try {
      Map<String, Object> testOrderData = new HashMap<>();
      testOrderData.put("id", Long.valueOf(999L));
      testOrderData.put("orderNo", "TEST" + System.currentTimeMillis());
      testOrderData.put("totalAmount", new BigDecimal("99.99"));
      testOrderData.put("userNickname", "测试用户");
      testOrderData.put("recipientName", "张三");
      testOrderData.put("recipientPhone", "13800138000");
      testOrderData.put("status", Integer.valueOf(1));
      testOrderData.put("createTime", LocalDateTime.now());
      NotificationWebSocket.sendNewOrderNotification(testOrderData);
      return Result.success("测试新订单通知发送成功");
    } catch (Exception e) {
      log.error("发送测试新订单通知失败", e);
      return Result.error("发送测试通知失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/test/order-status"})
  public Result<String> sendTestOrderStatusNotification() {
    try {
      Map<String, Object> testOrderData = new HashMap<>();
      testOrderData.put("id", Long.valueOf(888L));
      testOrderData.put("orderNo", "STATUS" + System.currentTimeMillis());
      testOrderData.put("status", Integer.valueOf(2));
      testOrderData.put("userNickname", "测试用户");
      NotificationWebSocket.sendOrderStatusChangeNotification(testOrderData);
      return Result.success("测试订单状态变更通知发送成功");
    } catch (Exception e) {
      log.error("发送测试订单状态变更通知失败", e);
      return Result.error("发送测试通知失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/test/low-stock"})
  public Result<String> sendTestLowStockNotification() {
    try {
      Map<String, Object> testStockData = new HashMap<>();
      testStockData.put("flowerId", Long.valueOf(777L));
      testStockData.put("flowerName", "红玫瑰");
      testStockData.put("stock", Integer.valueOf(5));
      testStockData.put("minStock", Integer.valueOf(10));
      NotificationWebSocket.sendLowStockNotification(testStockData);
      return Result.success("测试库存预警通知发送成功");
    } catch (Exception e) {
      log.error("发送测试库存预警通知失败", e);
      return Result.error("发送测试通知失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/status"})
  public Result<Map<String, Object>> getNotificationStatus() {
    Map<String, Object> status = new HashMap<>();
    status.put("onlineCount", Integer.valueOf(NotificationWebSocket.getOnlineCount()));
    status.put("timestamp", Long.valueOf(System.currentTimeMillis()));
    return Result.success("获取通知状态成功", status);
  }
}
