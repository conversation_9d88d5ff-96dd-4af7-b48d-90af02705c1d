package com.flower.service;

import com.flower.entity.UserAddress;
import java.util.List;

public interface AddressService {
  List<UserAddress> getUserAddresses(Long paramLong);
  
  UserAddress getAddressById(Long paramLong);
  
  UserAddress addAddress(UserAddress paramUserAddress);
  
  UserAddress updateAddress(UserAddress paramUserAddress);
  
  Boolean deleteAddress(Long paramLong1, Long paramLong2);
  
  Boolean setDefaultAddress(Long paramLong1, Long paramLong2);
  
  UserAddress getDefaultAddress(Long paramLong);
}
