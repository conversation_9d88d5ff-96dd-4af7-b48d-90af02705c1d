package com.flower.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.entity.CartItem;
import com.flower.entity.Flower;
import com.flower.entity.Order;
import com.flower.entity.OrderItem;
import com.flower.entity.User;
import com.flower.entity.UserPickupInfo;
import com.flower.mapper.CartItemMapper;
import com.flower.mapper.FlowerMapper;
import com.flower.mapper.OrderItemMapper;
import com.flower.mapper.OrderMapper;
import com.flower.mapper.UserMapper;
import com.flower.service.OrderService;
import com.flower.service.UserPickupInfoService;
import com.flower.service.impl.OrderNotificationData;
import com.flower.websocket.NotificationWebSocket;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import java.lang.invoke.SerializedLambda;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class OrderServiceImpl implements OrderService {
  @Autowired
  private OrderMapper orderMapper;

  @Autowired
  private OrderItemMapper orderItemMapper;

  @Autowired
  private CartItemMapper cartItemMapper;

  @Autowired
  private FlowerMapper flowerMapper;

  @Autowired
  private UserMapper userMapper;

  @Autowired
  private UserPickupInfoService userPickupInfoService;

  @Transactional
  public Order createOrderFromCart(Long userId, String recipientName, String recipientPhone, String recipientAddress, String deliveryNotes, String remark) {
    LambdaQueryWrapper<CartItem> cartWrapper = new LambdaQueryWrapper();
    cartWrapper.eq(CartItem::getUserId, userId);
    List<CartItem> cartItems = this.cartItemMapper.selectList((Wrapper)cartWrapper);
    if (cartItems.isEmpty())
      throw new RuntimeException("购物车为空");
    Order order = new Order();
    order.setOrderNo(generateOrderNo());
    order.setUserId(userId);
    order.setStatus(Integer.valueOf(2));
    order.setPaymentStatus(Integer.valueOf(0));
    order.setDeliveryStatus(Integer.valueOf(0));
    order.setRecipientName(recipientName);
    order.setRecipientPhone(recipientPhone);
    order.setRecipientAddress(recipientAddress);
    order.setDeliveryNotes(deliveryNotes);
    order.setRemark(remark);
    order.setCreatedAt(LocalDateTime.now());
    order.setUpdatedAt(LocalDateTime.now());
    BigDecimal totalAmount = BigDecimal.ZERO;
    for (CartItem cartItem : cartItems) {
      Flower flower = (Flower)this.flowerMapper.selectById(cartItem.getFlowerId());
      if (flower != null && flower.getStatus().intValue() == 1) {
        BigDecimal subtotal = flower.getPrice().multiply(new BigDecimal(cartItem.getQuantity().intValue()));
        totalAmount = totalAmount.add(subtotal);
      }
    }
    order.setTotalAmount(totalAmount);
    order.setDiscountAmount(BigDecimal.ZERO);
    BigDecimal finalAmount = totalAmount.subtract((order.getDiscountAmount() != null) ? order.getDiscountAmount() : BigDecimal.ZERO);
    order.setFinalAmount(finalAmount);
    this.orderMapper.insert(order);
    for (CartItem cartItem : cartItems) {
      Flower flower = (Flower)this.flowerMapper.selectById(cartItem.getFlowerId());
      if (flower != null && flower.getStatus().intValue() == 1) {
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderId(order.getId());
        orderItem.setFlowerId(flower.getId());
        orderItem.setFlowerName(flower.getName());
        orderItem.setFlowerImage(flower.getMainImage());
        orderItem.setPrice(flower.getPrice());
        orderItem.setQuantity(cartItem.getQuantity());
        orderItem.setSubtotal(flower.getPrice().multiply(new BigDecimal(cartItem.getQuantity().intValue())));
        orderItem.setCreatedAt(LocalDateTime.now());
        this.orderItemMapper.insert(orderItem);
      }
    }
    this.cartItemMapper.delete((Wrapper)cartWrapper);
    try {
      User user = (User)this.userMapper.selectById(userId);
      OrderNotificationData notificationData = new OrderNotificationData();
      notificationData.setId(order.getId());
      notificationData.setOrderNo(order.getOrderNo());
      notificationData.setTotalAmount(order.getTotalAmount());
      notificationData.setUserNickname((user != null) ? user.getNickname() : "未知用户");
      notificationData.setRecipientName(order.getRecipientName());
      notificationData.setRecipientPhone(order.getRecipientPhone());
      notificationData.setStatus(order.getStatus());
      notificationData.setCreateTime(order.getCreatedAt());
      NotificationWebSocket.sendNewOrderNotification(notificationData);
    } catch (Exception e) {
      System.err.println("发送新订单通知失败: " + e.getMessage());
    }
    return order;
  }

  @Transactional
  public Order createOrder(Long userId, List<Long> flowerIds, List<Integer> quantities, String recipientName, String recipientPhone, String recipientAddress, String deliveryNotes, String remark) {
    if (flowerIds.size() != quantities.size())
      throw new RuntimeException("Flower IDs and quantities size mismatch");
    Order order = new Order();
    order.setOrderNo(generateOrderNo());
    order.setUserId(userId);
    order.setStatus(Integer.valueOf(2));
    order.setPaymentStatus(Integer.valueOf(0));
    order.setDeliveryStatus(Integer.valueOf(0));
    order.setRecipientName(recipientName);
    order.setRecipientPhone(recipientPhone);
    order.setRecipientAddress(recipientAddress);
    order.setDeliveryNotes(deliveryNotes);
    order.setRemark(remark);
    order.setCreatedAt(LocalDateTime.now());
    order.setUpdatedAt(LocalDateTime.now());
    BigDecimal totalAmount = BigDecimal.ZERO;
    for (int i = 0; i < flowerIds.size(); i++) {
      Flower flower = (Flower)this.flowerMapper.selectById(flowerIds.get(i));
      if (flower != null && flower.getStatus().intValue() == 1) {
        BigDecimal subtotal = flower.getPrice().multiply(new BigDecimal(((Integer)quantities.get(i)).intValue()));
        totalAmount = totalAmount.add(subtotal);
      }
    }
    order.setTotalAmount(totalAmount);
    order.setDiscountAmount(BigDecimal.ZERO);
    BigDecimal finalAmount = totalAmount.subtract((order.getDiscountAmount() != null) ? order.getDiscountAmount() : BigDecimal.ZERO);
    order.setFinalAmount(finalAmount);
    this.orderMapper.insert(order);
    for (int j = 0; j < flowerIds.size(); j++) {
      Flower flower = (Flower)this.flowerMapper.selectById(flowerIds.get(j));
      if (flower != null && flower.getStatus().intValue() == 1) {
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderId(order.getId());
        orderItem.setFlowerId(flower.getId());
        orderItem.setFlowerName(flower.getName());
        orderItem.setFlowerImage(flower.getMainImage());
        orderItem.setPrice(flower.getPrice());
        orderItem.setQuantity(quantities.get(j));
        orderItem.setSubtotal(flower.getPrice().multiply(new BigDecimal(((Integer)quantities.get(j)).intValue())));
        orderItem.setCreatedAt(LocalDateTime.now());
        this.orderItemMapper.insert(orderItem);
      }
    }
    return order;
  }

  public Order getOrderById(Long orderId) {
    return (Order)this.orderMapper.selectById(orderId);
  }

  public List<OrderItem> getOrderItems(Long orderId) {
    LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper();
    wrapper.eq(OrderItem::getOrderId, orderId);
    return this.orderItemMapper.selectList((Wrapper)wrapper);
  }

  public PageResult<Order> getUserOrders(Long userId, Long current, Long size, Integer status) {
    Page<Order> page = new Page(current.longValue(), size.longValue());
    LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper();
    wrapper.eq(Order::getUserId, userId);
    if (status != null)
      wrapper.eq(Order::getStatus, status);
    wrapper.orderByDesc(Order::getCreatedAt);
    IPage<Order> result = this.orderMapper.selectPage((IPage)page, (Wrapper)wrapper);
    return PageResult.of(result.getRecords(), Long.valueOf(result.getTotal()), Long.valueOf(result.getSize()), Long.valueOf(result.getCurrent()));
  }

  public Order updateOrderStatus(Long orderId, Integer status) {
    Order order = (Order)this.orderMapper.selectById(orderId);
    if (order != null) {
      order.setStatus(status);
      order.setUpdatedAt(LocalDateTime.now());
      if (status.intValue() == 1) {
        order.setDeliveredAt(LocalDateTime.now());
        order.setDeliveryStatus(Integer.valueOf(2));
      } else if (status.intValue() == 2) {
        if (order.getPaymentStatus() == null)
          order.setPaymentStatus(Integer.valueOf(0));
      } else if (status.intValue() == 3) {
        order.setShippedAt(LocalDateTime.now());
        if (order.getDeliveryStatus() == null)
          order.setDeliveryStatus(Integer.valueOf(1));
      } else if (status.intValue() == 4) {
        if (order.getPaymentStatus().intValue() != 1) {
          order.setPaymentStatus(Integer.valueOf(1));
          order.setPaidAt(LocalDateTime.now());
        }
        order.setDeliveryStatus(Integer.valueOf(3));
      } else if (status.intValue() == 5) {

      }
      this.orderMapper.updateById(order);
    }
    return order;
  }

  public Boolean cancelOrder(Long orderId, Long userId) {
    Order order = (Order)this.orderMapper.selectById(orderId);
    if (order != null && order.getUserId().equals(userId) && order.getStatus().intValue() == 1) {
      order.setStatus(Integer.valueOf(5));
      order.setUpdatedAt(LocalDateTime.now());
      this.orderMapper.updateById(order);
      return Boolean.valueOf(true);
    }
    return Boolean.valueOf(false);
  }

  @Transactional
  public Boolean payOrder(Long orderId, String paymentMethod) {
    Order order = (Order)this.orderMapper.selectById(orderId);
    if (order != null && order.getStatus().intValue() == 1 && order.getPaymentStatus().intValue() == 0) {
      order.setStatus(Integer.valueOf(2));
      order.setPaymentStatus(Integer.valueOf(1));
      order.setPaymentMethod(paymentMethod);
      order.setPaidAt(LocalDateTime.now());
      order.setUpdatedAt(LocalDateTime.now());
      this.orderMapper.updateById(order);
      List<OrderItem> orderItems = getOrderItems(orderId);
      for (OrderItem item : orderItems) {
        Flower flower = (Flower)this.flowerMapper.selectById(item.getFlowerId());
        if (flower != null) {
          flower.setSalesCount(Integer.valueOf(flower.getSalesCount().intValue() + item.getQuantity().intValue()));
          flower.setStockQuantity(Integer.valueOf(flower.getStockQuantity().intValue() - item.getQuantity().intValue()));
          this.flowerMapper.updateById(flower);
        }
      }
      return Boolean.valueOf(true);
    }
    return Boolean.valueOf(false);
  }

  @Transactional
  public Order createDirectOrder(Long userId, List<Long> flowerIds, List<Integer> quantities, Integer deliveryType, String recipientName, String recipientPhone, String recipientAddress, String deliveryTime, String pickupName, String pickupPhone, String pickupTime, String remark, String backupAddress) {
    if (flowerIds.size() != quantities.size())
      throw new RuntimeException("商品ID和数量不匹配");
    Order order = new Order();
    order.setOrderNo(generateOrderNo());
    order.setUserId(userId);
    order.setStatus(Integer.valueOf(2));
    order.setPaymentStatus(Integer.valueOf(0));
    order.setDeliveryStatus(Integer.valueOf(0));
    order.setDeliveryType(deliveryType);
    order.setRemark(remark);
    order.setCreatedAt(LocalDateTime.now());
    order.setUpdatedAt(LocalDateTime.now());
    if (deliveryType.intValue() == 1) {
      order.setRecipientName(recipientName);
      order.setRecipientPhone(recipientPhone);
      order.setRecipientAddress(recipientAddress);
      order.setDeliveryTime(deliveryTime);
    } else {
      order.setPickupName(pickupName);
      order.setPickupPhone(pickupPhone);
      order.setPickupTime(pickupTime);
      if (backupAddress != null && !backupAddress.trim().isEmpty())
        order.setRecipientAddress(backupAddress);
      UserPickupInfo pickupInfo = new UserPickupInfo();
      pickupInfo.setUserId(userId);
      pickupInfo.setPickupName(pickupName);
      pickupInfo.setPickupPhone(pickupPhone);
      pickupInfo.setPickupTime(pickupTime);
      pickupInfo.setRemark(remark);
      pickupInfo.setBackupAddress(backupAddress);
      this.userPickupInfoService.saveOrUpdate(pickupInfo);
    }
    BigDecimal totalAmount = BigDecimal.ZERO;
    for (int i = 0; i < flowerIds.size(); i++) {
      Flower flower = (Flower)this.flowerMapper.selectById(flowerIds.get(i));
      if (flower == null || flower.getStatus().intValue() != 1)
        throw new RuntimeException("商品不存在或已下架");
      if (flower.getStockQuantity().intValue() < ((Integer)quantities.get(i)).intValue())
        throw new RuntimeException("商品库存不足：" + flower.getName());
      BigDecimal subtotal = flower.getPrice().multiply(new BigDecimal(((Integer)quantities.get(i)).intValue()));
      totalAmount = totalAmount.add(subtotal);
    }
    order.setTotalAmount(totalAmount);
    order.setDiscountAmount(BigDecimal.ZERO);
    BigDecimal finalAmount = totalAmount.subtract((order.getDiscountAmount() != null) ? order.getDiscountAmount() : BigDecimal.ZERO);
    order.setFinalAmount(finalAmount);
    this.orderMapper.insert(order);
    for (int j = 0; j < flowerIds.size(); j++) {
      Flower flower = (Flower)this.flowerMapper.selectById(flowerIds.get(j));
      if (flower != null && flower.getStatus().intValue() == 1) {
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderId(order.getId());
        orderItem.setFlowerId(flower.getId());
        orderItem.setFlowerName(flower.getName());
        orderItem.setFlowerImage(flower.getMainImage());
        orderItem.setPrice(flower.getPrice());
        orderItem.setQuantity(quantities.get(j));
        orderItem.setSubtotal(flower.getPrice().multiply(new BigDecimal(((Integer)quantities.get(j)).intValue())));
        orderItem.setCreatedAt(LocalDateTime.now());
        this.orderItemMapper.insert(orderItem);
        flower.setSalesCount(Integer.valueOf(flower.getSalesCount().intValue() + ((Integer)quantities.get(j)).intValue()));
        flower.setStockQuantity(Integer.valueOf(flower.getStockQuantity().intValue() - ((Integer)quantities.get(j)).intValue()));
        this.flowerMapper.updateById(flower);
      }
    }
    return order;
  }

  public Boolean confirmPayment(Long orderId) {
    Order order = (Order)this.orderMapper.selectById(orderId);
    if (order != null && order.getStatus().intValue() == 1 && order.getPaymentStatus().intValue() == 0) {
      order.setPaymentStatus(Integer.valueOf(1));
      order.setPaidAt(LocalDateTime.now());
      order.setUpdatedAt(LocalDateTime.now());
      this.orderMapper.updateById(order);
      return Boolean.valueOf(true);
    }
    return Boolean.valueOf(false);
  }

  public Map<String, Integer> getOrderStatsByUserId(Long userId) {
    Map<String, Integer> stats = new HashMap<>();
    LambdaQueryWrapper<Order> pendingWrapper = new LambdaQueryWrapper<>();
    pendingWrapper.eq((SFunction<Order, ?>) Order::getUserId, userId)
      .eq((SFunction<Order, ?>) Order::getStatus, Integer.valueOf(1))
      .eq((SFunction<Order, ?>) Order::getPaymentStatus, Integer.valueOf(0));
    int pendingCount = Math.toIntExact(this.orderMapper.selectCount((Wrapper)pendingWrapper).longValue());
    LambdaQueryWrapper<Order> orderedWrapper = new LambdaQueryWrapper<>();
    orderedWrapper.eq((SFunction<Order, ?>) Order::getUserId, userId)
      .eq((SFunction<Order, ?>) Order::getStatus, Integer.valueOf(2));
    int orderedCount = Math.toIntExact(this.orderMapper.selectCount((Wrapper)orderedWrapper).longValue());
    LambdaQueryWrapper<Order> shippingWrapper = new LambdaQueryWrapper<>();
    shippingWrapper.eq((SFunction<Order, ?>) Order::getUserId, userId)
      .eq((SFunction<Order, ?>) Order::getStatus, Integer.valueOf(3));
    int shippingCount = Math.toIntExact(this.orderMapper.selectCount((Wrapper)shippingWrapper).longValue());
    LambdaQueryWrapper<Order> toReceiveWrapper = new LambdaQueryWrapper<>();
    toReceiveWrapper.eq((SFunction<Order, ?>) Order::getUserId, userId)
      .eq((SFunction<Order, ?>) Order::getStatus, Integer.valueOf(1))
      .eq((SFunction<Order, ?>) Order::getPaymentStatus, Integer.valueOf(1));
    int toReceiveCount = Math.toIntExact(this.orderMapper.selectCount((Wrapper)toReceiveWrapper).longValue());
    LambdaQueryWrapper<Order> completedWrapper = new LambdaQueryWrapper<>();
    completedWrapper.eq((SFunction<Order, ?>) Order::getUserId, userId)
      .eq((SFunction<Order, ?>) Order::getStatus, Integer.valueOf(4));
    int completedCount = Math.toIntExact(this.orderMapper.selectCount((Wrapper)completedWrapper).longValue());
    stats.put("pending", Integer.valueOf(pendingCount));
    stats.put("ordered", Integer.valueOf(orderedCount));
    stats.put("shipping", Integer.valueOf(shippingCount));
    stats.put("toReceive", Integer.valueOf(toReceiveCount));
    stats.put("completed", Integer.valueOf(completedCount));
    return stats;
  }

  private String generateOrderNo() {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    String random = IdUtil.randomUUID().substring(0, 6).toUpperCase();
    return "FL" + timestamp + random;
  }
}
