package com.flower.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {
  private String swiperPath = "src/main/resources/image/swiper/";
  
  private String imageUrlPrefix = "/image/swiper/";
  
  private String serverBaseUrl = "https://mxm.qiangs.xyz";
  
  private String[] allowedImageTypes = new String[] { "jpg", "jpeg", "png", "gif" };
  
  private long maxFileSize = 2097152L;
  
  public String getSwiperPath() {
    return this.swiperPath;
  }
  
  public void setSwiperPath(String swiperPath) {
    this.swiperPath = swiperPath;
  }
  
  public String getImageUrlPrefix() {
    return this.imageUrlPrefix;
  }
  
  public void setImageUrlPrefix(String imageUrlPrefix) {
    this.imageUrlPrefix = imageUrlPrefix;
  }
  
  public String[] getAllowedImageTypes() {
    return this.allowedImageTypes;
  }
  
  public void setAllowedImageTypes(String[] allowedImageTypes) {
    this.allowedImageTypes = allowedImageTypes;
  }
  
  public long getMaxFileSize() {
    return this.maxFileSize;
  }
  
  public void setMaxFileSize(long maxFileSize) {
    this.maxFileSize = maxFileSize;
  }
  
  public String getServerBaseUrl() {
    return this.serverBaseUrl;
  }
  
  public void setServerBaseUrl(String serverBaseUrl) {
    this.serverBaseUrl = serverBaseUrl;
  }
  
  public String getFullSwiperPath() {
    return System.getProperty("user.dir") + "/" + System.getProperty("user.dir");
  }
  
  public String getFullImageUrl(String filename) {
    return this.serverBaseUrl + this.serverBaseUrl + this.imageUrlPrefix;
  }
}
