// index.js
const API = require('../../config/api.js')
const request = require('../../utils/request.js')
const { CATEGORY_ICONS, DEFAULT_CATEGORY_ICON } = require('../../config/constants.js')

Page({
    data: {
        swiperList: [],
        categoryList: [],
        productList: [],
        loading: false
    },

    onLoad() {
        this.loadPageData()
    },

    onShow() {
        // 页面显示时刷新数据
        this.loadPageData()
    },

    onPullDownRefresh() {
        this.loadPageData().finally(() => {
            wx.stopPullDownRefresh()
        })
    },

    /**
     * 加载页面数据
     */
    async loadPageData() {
        if (this.data.loading) return

        this.setData({ loading: true })

        try {
            await Promise.all([
                this.loadSwiperData(),
                this.loadCategoryData(),
                this.loadProductData()
            ])
        } catch (error) {
            console.error('加载页面数据失败:', error)
            wx.showToast({
                title: '加载失败，请重试',
                icon: 'none'
            })
        } finally {
            this.setData({ loading: false })
        }
    },

    /**
     * 加载轮播图数据
     */
    async loadSwiperData() {
        try {
            const data = await request.get(API.SWIPER.ACTIVE)
            this.setData({
                swiperList: data || []
            })
        } catch (error) {
            console.error('加载轮播图失败:', error)
        }
    },

    /**
     * 加载分类数据
     */
    async loadCategoryData() {
        try {
            const data = await request.get(API.FLOWER.CATEGORIES)
            const categoryList = (data || []).slice(0, 8).map(item => ({
                ...item,
                icon: CATEGORY_ICONS[item.name] || DEFAULT_CATEGORY_ICON
            }))
            this.setData({ categoryList })
        } catch (error) {
            console.error('加载分类数据失败:', error)
        }
    },

    /**
     * 加载商品数据
     */
    async loadProductData() {
        try {
            const data = await request.get(API.FLOWER.LIST, {
                current: 1,
                size: 6,
                excludeFeatured: false
            })

            const productList = (data ? .records || []).map(item => ({
                ...item,
                imageUrl: item.imageUrl || '/images/default-product.png',
                tags: this.parseProductTags(item),
                soldCount: item.soldCount || 0
            }))

            this.setData({ productList })
        } catch (error) {
            console.error('加载商品数据失败:', error)
        }
    },

    /**
     * 解析商品标签
     */
    parseProductTags(product) {
        const tags = []
        if (product.categoryName) tags.push(product.categoryName)
        if (product.color) tags.push(product.color)
        if (product.size) tags.push(product.size)
        return tags.slice(0, 3) // 最多显示3个标签
    },

    /**
     * 搜索点击事件
     */
    onSearchTap() {
        wx.navigateTo({
            url: '/pages/search/search'
        })
    },

    /**
     * 轮播图点击事件
     */
    onSwiperTap(e) {
        const item = e.currentTarget.dataset.item
        if (item.linkUrl) {
            wx.navigateTo({
                url: item.linkUrl
            })
        }
    },

    /**
     * 分类点击事件
     */
    onCategoryTap(e) {
        const category = e.currentTarget.dataset.category
        wx.navigateTo({
            url: `/pages/category/category?categoryId=${category.id}&categoryName=${category.name}`
        })
    },

    /**
     * 商品点击事件
     */
    onProductTap(e) {
        const product = e.currentTarget.dataset.product
        wx.navigateTo({
            url: `/pages/product/detail?id=${product.id}`
        })
    },

    /**
     * 更多商品点击事件
     */
    onMoreTap() {
        wx.switchTab({
            url: '/pages/category/category'
        })
    },

    /**
     * 添加到购物车
     */
    async onAddToCart(e) {
        const product = e.currentTarget.dataset.product

        try {
            // 这里需要用户登录状态，暂时使用模拟用户ID
            const userId = 1

            await request.post(API.CART.ADD, {
                userId,
                flowerId: product.id,
                quantity: 1
            })

            wx.showToast({
                title: '已添加到购物车',
                icon: 'success'
            })
        } catch (error) {
            console.error('添加到购物车失败:', error)
            wx.showToast({
                title: '添加失败，请重试',
                icon: 'none'
            })
        }
    }
})