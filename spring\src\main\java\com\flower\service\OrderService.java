package com.flower.service;

import com.flower.common.PageResult;
import com.flower.entity.Order;
import com.flower.entity.OrderItem;
import java.util.List;
import java.util.Map;

public interface OrderService {
  Order createOrderFromCart(Long paramLong, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5);
  
  Order createOrder(Long paramLong, List<Long> paramList, List<Integer> paramList1, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5);
  
  Order getOrderById(Long paramLong);
  
  List<OrderItem> getOrderItems(Long paramLong);
  
  PageResult<Order> getUserOrders(Long paramLong1, Long paramLong2, Long paramLong3, Integer paramInteger);
  
  Order updateOrderStatus(Long paramLong, Integer paramInteger);
  
  Boolean cancelOrder(Long paramLong1, Long paramLong2);
  
  Boolean payOrder(Long paramLong, String paramString);
  
  Boolean confirmPayment(Long paramLong);
  
  Order createDirectOrder(Long paramLong, List<Long> paramList, List<Integer> paramList1, Integer paramInteger, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9);
  
  Map<String, Integer> getOrderStatsByUserId(Long paramLong);
}
