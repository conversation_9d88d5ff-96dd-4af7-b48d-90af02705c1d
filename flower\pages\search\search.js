// search.js
const API = require('../../config/api.js')
const request = require('../../utils/request.js')

Page({
  data: {
    keyword: '',
    searchResults: [],
    searchHistory: [],
    hotKeywords: ['商务礼品', '年会礼品', '婚庆礼品', '节日礼品', '家纺礼品'],
    loading: false,
    totalCount: 0
  },

  onLoad(options) {
    this.loadSearchHistory()
  },

  /**
   * 加载搜索历史
   */
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory') || []
      this.setData({
        searchHistory: history.slice(0, 10) // 最多显示10条历史记录
      })
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  },

  /**
   * 保存搜索历史
   */
  saveSearchHistory(keyword) {
    try {
      let history = wx.getStorageSync('searchHistory') || []
      
      // 移除重复项
      history = history.filter(item => item !== keyword)
      
      // 添加到开头
      history.unshift(keyword)
      
      // 限制数量
      history = history.slice(0, 20)
      
      wx.setStorageSync('searchHistory', history)
      this.setData({
        searchHistory: history.slice(0, 10)
      })
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  },

  /**
   * 关键词输入事件
   */
  onKeywordChange(e) {
    this.setData({
      keyword: e.detail.value
    })
  },

  /**
   * 搜索事件
   */
  async onSearch() {
    const keyword = this.data.keyword.trim()
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })
    
    try {
      const data = await request.get(API.FLOWER.LIST, {
        current: 1,
        size: 20,
        keyword: keyword
      })
      
      const searchResults = (data?.records || []).map(item => ({
        ...item,
        imageUrl: item.imageUrl || '/images/default-product.png'
      }))
      
      this.setData({
        searchResults,
        totalCount: data?.total || 0
      })
      
      // 保存搜索历史
      this.saveSearchHistory(keyword)
      
    } catch (error) {
      console.error('搜索失败:', error)
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 历史记录点击事件
   */
  onHistoryTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ keyword })
    this.onSearch()
  },

  /**
   * 热门搜索点击事件
   */
  onHotTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ keyword })
    this.onSearch()
  },

  /**
   * 清空搜索历史
   */
  onClearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('searchHistory')
            this.setData({
              searchHistory: []
            })
            wx.showToast({
              title: '已清空',
              icon: 'success'
            })
          } catch (error) {
            console.error('清空搜索历史失败:', error)
          }
        }
      }
    })
  },

  /**
   * 商品点击事件
   */
  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    wx.navigateTo({
      url: `/pages/product/detail?id=${product.id}`
    })
  },

  /**
   * 取消搜索
   */
  onCancel() {
    wx.navigateBack()
  }
})
