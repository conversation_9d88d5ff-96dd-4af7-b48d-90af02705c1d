package com.flower.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class FileUploadService {
  private static final Logger log = LoggerFactory.getLogger(com.flower.service.FileUploadService.class);
  
  public Map<String, String> uploadAdminAvatar(MultipartFile file, Long adminId) throws IOException {
    validateImageFile(file);
    String uploadDir = getAdminImageUploadDir();
    createDirectoryIfNotExists(uploadDir);
    String originalFilename = file.getOriginalFilename();
    String extension = getFileExtension(originalFilename);
    String filename = "avatar_" + adminId + "_" + System.currentTimeMillis() + extension;
    Path targetPath = Paths.get(uploadDir, new String[] { filename });
    log.info("保存头像文件: {}", targetPath.toAbsolutePath());
    try {
      Files.copy(file.getInputStream(), targetPath, new java.nio.file.CopyOption[0]);
      log.info("头像文件保存成功: {}", targetPath.toAbsolutePath());
    } catch (IOException e) {
      log.error("头像文件保存失败: {}", e.getMessage(), e);
      throw new IOException("文件保存失败: " + e.getMessage());
    } 
    String avatarUrl = "https://mxm.qiangs.xyz/api/image/admin-image/" + filename;
    Map<String, String> result = new HashMap<>();
    result.put("url", avatarUrl);
    result.put("filename", filename);
    result.put("path", targetPath.toAbsolutePath().toString());
    return result;
  }
  
  private String getAdminImageUploadDir() {
    String jarPath = System.getProperty("user.dir");
    String uploadDir = jarPath + jarPath + "image" + File.separator + "admin-image";
    log.info("管理员头像上传目录: {}", uploadDir);
    return uploadDir;
  }
  
  private String getClasspathAdminImageDir() {
    String jarPath = System.getProperty("user.dir");
    String classpathDir = jarPath + jarPath + "image" + File.separator + "admin-image";
    log.info("管理员头像classpath目录: {}", classpathDir);
    return classpathDir;
  }
  
  private void createDirectoryIfNotExists(String dirPath) throws IOException {
    Path path = Paths.get(dirPath, new String[0]);
    if (!Files.exists(path, new java.nio.file.LinkOption[0]))
      try {
        Files.createDirectories(path, (FileAttribute<?>[])new FileAttribute[0]);
        log.info("创建目录成功: {}", path.toAbsolutePath());
      } catch (IOException e) {
        log.error("创建目录失败: {}", e.getMessage(), e);
        throw new IOException("创建上传目录失败: " + e.getMessage());
      }  
  }
  
  private void validateImageFile(MultipartFile file) throws IOException {
    if (file.isEmpty())
      throw new IOException("文件不能为空"); 
    String contentType = file.getContentType();
    if (contentType == null || !contentType.startsWith("image/"))
      throw new IOException("只能上传图片文件"); 
    if (file.getSize() > 2097152L)
      throw new IOException("文件大小不能超过2MB"); 
    String originalFilename = file.getOriginalFilename();
    if (originalFilename == null || originalFilename.trim().isEmpty())
      throw new IOException("文件名不能为空"); 
    String extension = getFileExtension(originalFilename).toLowerCase();
    if (!extension.matches("\\.(jpg|jpeg|png)$"))
      throw new IOException("只支持JPG、JPEG、PNG格式的图片"); 
  }
  
  private String getFileExtension(String filename) {
    if (filename == null || filename.trim().isEmpty())
      return ""; 
    int lastDotIndex = filename.lastIndexOf(".");
    if (lastDotIndex == -1)
      return ""; 
    return filename.substring(lastDotIndex);
  }
}
