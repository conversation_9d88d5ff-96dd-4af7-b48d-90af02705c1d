package com.flower;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@MapperScan({"com.flower.mapper"})
public class FlowerShopApplication {
  public static void main(String[] args) {
    SpringApplication.run(com.flower.FlowerShopApplication.class, args);
    System.out.println("=================================");
    System.out.println("花语小铺后端服务启动成功！");
    System.out.println("API接口地址: http://localhost:8080/api");
    System.out.println("=================================");
  }
}
