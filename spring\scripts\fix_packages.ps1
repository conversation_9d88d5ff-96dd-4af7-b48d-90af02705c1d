$ErrorActionPreference = 'Stop'
$root = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location (Resolve-Path "$root\..")

$javaDir = "src/main/java"
if (-not (Test-Path $javaDir)) {
  Write-Host "Java source dir not found: $javaDir"
  exit 1
}

$files = Get-ChildItem -Recurse -Filter *.java -Path $javaDir
foreach ($f in $files) {
  $path = $f.FullName
  $content = Get-Content -Raw -LiteralPath $path
  $newContent = $content -replace 'package BOOT-INF\.classes\.', 'package '
  if ($newContent -ne $content) {
    Set-Content -LiteralPath $path -Value $newContent -Encoding UTF8
    Write-Host "Updated: $path"
  }
}

Write-Host "Done."

