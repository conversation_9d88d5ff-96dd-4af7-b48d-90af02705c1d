<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>花语小铺 · 接口文档与在线测试</title>
  <style>
    :root { --bg:#0e1116; --card:#151a22; --muted:#8ea0b5; --text:#e6eef8; --pri:#5aa9ff; --ok:#22c55e; --err:#ef4444; }
    * { box-sizing: border-box; }
    body { margin:0; font-family: ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial; background: var(--bg); color: var(--text);}    
    header { padding:20px 24px; border-bottom:1px solid #1f2632; position: sticky; top:0; background: linear-gradient(180deg,#0e1116,#0e1116cc 70%,#0e111600);}    
    h1 { margin:0 0 6px; font-size:20px; }
    .sub { color: var(--muted); font-size:12px; }
    .container { display:grid; grid-template-columns: 320px 1fr; gap:18px; padding:18px; }
    .panel { background: var(--card); border:1px solid #1f2632; border-radius:12px; }
    .panel h2 { margin:0; padding:14px 16px; border-bottom:1px solid #1f2632; font-size:14px; color:#cfe0f5; }
    .panel .body { padding:14px 16px; }
    input, select, textarea { width: 100%; padding:10px 12px; background:#0f141c; color:var(--text); border:1px solid #2a3342; border-radius:10px; outline:none; }
    input:focus, textarea:focus, select:focus { border-color: var(--pri); box-shadow: 0 0 0 3px #5aa9ff22; }
    .grid2 { display:grid; grid-template-columns: 1fr 1fr; gap:12px; }
    .row { display:flex; gap:10px; }
    .btn { padding:10px 14px; border:1px solid #2a3342; background:#0f141c; color:var(--text); border-radius:10px; cursor:pointer; }
    .btn.primary { background: linear-gradient(180deg,#4ea3ff,#2b7fff); border-color:#246ee9; }
    .btn.ghost { background: transparent; }
    .tag { display:inline-block; padding:2px 8px; border-radius:100px; border:1px solid #2a3342; color:#a9bbd4; font-size:12px; margin-right:6px; }
    .method { font-weight:700; color:#fff; }
    .GET { color:#22c55e; }
    .POST { color:#5aa9ff; }
    .PUT { color:#f59e0b; }
    .DELETE { color:#ef4444; }
    .group { margin-bottom:16px; }
    details.endpoint { border:1px solid #223044; border-radius:10px; margin:8px 0; overflow:hidden; }
    details.endpoint > summary { list-style:none; padding:12px 14px; background:#101621; cursor:pointer; display:flex; align-items:center; gap:8px; }
    .path { font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, monospace; color:#cfe0f5; }
    .desc { color:#a9bbd4; font-size:12px; }
    .ep-body { padding:12px 14px; border-top:1px solid #223044; }
    pre { white-space: pre-wrap; word-wrap: break-word; background:#0c1118; padding:10px; border-radius:10px; border:1px solid #1f2632; }
    .footer { padding:16px; text-align:center; color:#93a6bf; }
    .ok { color: var(--ok) }
    .err { color: var(--err) }
  </style>
</head>
<body>
  <header>
    <h1>花语小铺 · API 接口文档</h1>
    <div class="sub">后端已启动：默认前缀 http://localhost:8080/api（application.yml 已设置 server.servlet.context-path=/api）</div>
  </header>
  <div class="container">
    <div class="panel">
      <h2>环境与全局设置</h2>
      <div class="body">
        <label>Base URL（可修改）</label>
        <input id="baseUrl" value="http://localhost:8080/api" />
        <div style="height:10px"></div>
        <label>公共请求头（JSON，示例：{"Authorization":"Bearer <token>"}）</label>
        <textarea id="commonHeaders" rows="4">{}</textarea>
        <div style="height:10px"></div>
        <label>搜索接口</label>
        <input id="search" placeholder="输入关键字过滤接口..." />
      </div>
    </div>

    <div class="panel">
      <h2>在线测试器</h2>
      <div class="body">
        <div class="grid2">
          <div>
            <label>HTTP Method</label>
            <select id="reqMethod">
              <option>GET</option>
              <option>POST</option>
              <option>PUT</option>
              <option>DELETE</option>
            </select>
          </div>
          <div>
            <label>请求路径（不含前缀）</label>
            <input id="reqPath" placeholder="/flower/list?current=1&size=10" />
          </div>
        </div>
        <div style="height:10px"></div>
        <label>请求头（JSON）</label>
        <textarea id="reqHeaders" rows="4">{"Content-Type":"application/json"}</textarea>
        <div style="height:10px"></div>
        <label>请求体（JSON / multipart 请在请求头中修改）</label>
        <textarea id="reqBody" rows="6">{}</textarea>
        <div style="height:10px"></div>
        <div class="row">
          <button class="btn primary" onclick="sendRequest()">发送请求</button>
          <button class="btn ghost" onclick="clearResult()">清空结果</button>
        </div>
        <div style="height:10px"></div>
        <div id="resultMeta" class="sub"></div>
        <pre id="result"></pre>
      </div>
    </div>
  </div>

  <div class="panel" style="margin: 0 18px 18px;">
    <h2>接口目录（点击可展开详情并一键带入测试器）</h2>
    <div class="body" id="endpoints"></div>
  </div>

  <div class="footer">© 花语小铺 · 自动生成接口清单，基于后端源码整理。注意：NotificationController 使用了类级路径 /api/notification，叠加全局 /api 后完整前缀为 /api/api/notification。</div>

  <script>
    const groups = [
      { name:"前台 · 花卉与地区（/flower）", base:"/flower", items:[
        { m:"GET", p:"/categories", d:"获取所有分类" },
        { m:"GET", p:"/price-categories", d:"获取启用的价格区间" },
        { m:"GET", p:"/list", q:"current,size,categoryId,keyword,minPrice,maxPrice,excludeFeatured", d:"分页获取商品（支持分类、关键词、价格区间与是否排除精选）" },
        { m:"GET", p:"/detail/{id}", d:"获取商品详情" },
        { m:"GET", p:"/featured", q:"limit,categoryId,minPrice,maxPrice", d:"获取精选商品列表" },
        { m:"GET", p:"/search", q:"keyword,current,size", d:"关键词搜索商品（会记录热词）" },
        { m:"GET", p:"/category/{categoryId}", q:"current,size", d:"按分类分页获取商品" },
        { m:"GET", p:"/region/data", d:"获取省市区数据（缓存）" },
        { m:"POST", p:"/region/clear-cache", d:"清空地区缓存" },
        { m:"GET", p:"/hot-keywords", d:"获取热门搜索关键词" },
      ]},
      { name:"前台 · 地址（/address）", base:"/address", items:[
        { m:"GET", p:"/list/{userId}", d:"获取用户的收货地址列表" },
        { m:"GET", p:"/detail/{id}", d:"地址详情" },
        { m:"POST", p:"/add", d:"新增地址（JSON 体）" },
        { m:"PUT", p:"/update", d:"更新地址（JSON 体）" },
        { m:"DELETE", p:"/delete", d:"删除地址（JSON 体：id,userId）" },
        { m:"POST", p:"/setDefault", d:"设置默认地址（JSON 体：id,userId）" },
        { m:"GET", p:"/default/{userId}", d:"获取默认地址" },
      ]},
      { name:"前台 · 购物车（/cart）", base:"/cart", items:[
        { m:"POST", p:"/add", d:"加入购物车（JSON 体：userId,flowerId,quantity）" },
        { m:"PUT",  p:"/update", d:"更新购物车数量（JSON 体）" },
        { m:"DELETE",p:"/remove", d:"移出购物车（JSON 体）" },
        { m:"GET",  p:"/list/{userId}", d:"获取购物车列表" },
        { m:"DELETE",p:"/batch-remove", d:"批量移除（JSON 体：userId,flowerIds[]）" },
        { m:"DELETE",p:"/clear/{userId}", d:"清空购物车" },
        { m:"GET",  p:"/count/{userId}", d:"购物车商品数量" },
      ]},
      { name:"前台 · 收藏（/favorite）", base:"/favorite", items:[
        { m:"POST", p:"/add", d:"添加收藏（JSON 体：userId,flowerId）" },
        { m:"DELETE",p:"/remove", d:"取消收藏（JSON 体）" },
        { m:"GET",  p:"/check", q:"userId,flowerId", d:"是否已收藏" },
        { m:"POST", p:"/toggle", d:"切换收藏（JSON 体）" },
        { m:"GET",  p:"/list/{userId}", d:"收藏记录列表" },
        { m:"GET",  p:"/flowers/{userId}", d:"收藏的商品列表" },
        { m:"GET",  p:"/count/{userId}", d:"收藏数量" },
      ]},
      { name:"前台 · 订单（/order）", base:"/order", items:[
        { m:"POST", p:"/create/cart", d:"从购物车创建订单（JSON 体）" },
        { m:"POST", p:"/create", d:"直接创建订单（JSON 体：flowerIds,quantities,...）" },
        { m:"GET",  p:"/detail/{orderId}", d:"订单详情" },
        { m:"GET",  p:"/items/{orderId}", d:"订单商品列表" },
        { m:"GET",  p:"/list/{userId}", q:"current,size,status", d:"用户订单分页列表" },
        { m:"PUT",  p:"/cancel", d:"取消订单（JSON 体：orderId,userId）" },
        { m:"PUT",  p:"/pay", d:"支付订单（JSON 体）" },
        { m:"POST", p:"/create-direct", d:"直接下单（JSON 体：items[] 等）" },
        { m:"PUT",  p:"/confirm-receipt", d:"确认收货（JSON 体）" },
        { m:"GET",  p:"/stats/{userId}", d:"用户订单统计（待付款/已下单/配送中/待收货/已完成等）" },
      ]},
      { name:"前台 · 自取信息（/pickup-info）", base:"/pickup-info", items:[
        { m:"GET", p:"/{userId}", d:"获取用户自取信息" },
        { m:"POST", p:"/save", d:"保存/更新自取信息（JSON 体）" },
        { m:"DELETE", p:"/{userId}", d:"删除自取信息" },
      ]},
      { name:"前台 · 用户（/user）", base:"/user", items:[
        { m:"POST", p:"/login", d:"小程序登录（JSON 体：code）" },
        { m:"GET",  p:"/info/{openid}", d:"获取用户信息" },
        { m:"PUT",  p:"/update", d:"更新用户信息（JSON 体）" },
        { m:"POST", p:"/upload-avatar", d:"上传头像（multipart：file,userId）" },
        { m:"POST", p:"/update-profile", d:"更新昵称/头像（Base64）" },
        { m:"PUT",  p:"/phone", d:"直接设置手机号（JSON 体）" },
        { m:"POST", p:"/phone", d:"通过微信 code 获取手机号（JSON 体）" },
        { m:"GET",  p:"/config", d:"检查配置" },
        { m:"POST", p:"/decrypt-phone", d:"解密手机号（JSON 体）" },
        { m:"POST", p:"/phone-verify", d:"手机号验证码校验（JSON 体）" },
        { m:"POST", p:"/send-sms", d:"发送短信验证码（JSON 体）" },
      ]},
      { name:"静态/图片（/image, /image/swiper）", base:"/image", items:[
        { m:"GET", p:"/user-image/{filename}", d:"用户头像访问" },
        { m:"GET", p:"/shop-image/{filename}", d:"商品图片访问" },
        { m:"GET", p:"/test", d:"图片目录自检" },
        { m:"GET", p:"/swiper/{filename}", d:"轮播图图片访问（类 SwiperImageController）" },
      ]},
      { name:"后台 · 管理（/admin）", base:"/admin", items:[
        { m:"GET", p:"/captcha", d:"生成登录验证码" },
        { m:"POST", p:"/login", d:"管理员登录" },
        { m:"GET", p:"/verify", d:"校验登录 token" },
        { m:"POST", p:"/logout", d:"退出登录" },
        { m:"GET", p:"/stats", d:"仪表盘统计" },
        { m:"GET", p:"/chart/{type}", d:"图表数据（trend/category-sales/sales/users/stock-status/order-status/hot-products）" },
        { m:"GET", p:"/users", d:"用户分页列表，支持 keyword/status" },
        { m:"GET", p:"/users/{id}", d:"用户详情" },
        { m:"PUT", p:"/users/{id}", d:"更新用户信息" },
        { m:"PUT", p:"/users/{id}/status", d:"更新用户状态" },
        { m:"DELETE", p:"/users/{id}", d:"删除用户" },
        { m:"DELETE", p:"/users/batch", d:"批量删除用户（JSON 体：ids[]）" },
        { m:"GET", p:"/admin-users", d:"后台用户分页：keyword/status/role" },
        { m:"GET", p:"/admin-users/{id}", d:"后台用户详情" },
        { m:"POST", p:"/admin-users", d:"创建后台用户" },
        { m:"PUT", p:"/admin-users/{id}", d:"更新后台用户" },
        { m:"PUT", p:"/admin-users/{id}/status", d:"更新后台用户状态" },
        { m:"DELETE", p:"/admin-users/{id}", d:"删除后台用户" },
        { m:"DELETE", p:"/admin-users/batch", d:"批量删除后台用户（JSON 体：ids[]）" },
        { m:"PUT", p:"/admin-users/batch/status", d:"批量更新后台用户状态（JSON 体：ids[],status）" },
        { m:"GET", p:"/orders", d:"订单分页（keyword/status/sortBy/sortOrder）" },
        { m:"GET", p:"/orders/{id}", d:"订单详情（含明细）" },
        { m:"PUT", p:"/orders/{id}/status", d:"修改订单状态" },
        { m:"PUT", p:"/orders/{id}", d:"编辑订单（收件人/地址/备注/配送时间等）" },
        { m:"PUT", p:"/orders/{id}/confirm-payment", d:"确认付款" },
        { m:"DELETE", p:"/orders/{id}", d:"删除订单" },
        { m:"DELETE", p:"/orders/batch", d:"批量删除订单（JSON 体：ids[]）" },
        { m:"GET", p:"/categories", d:"分类分页（keyword/status）" },
        { m:"POST", p:"/categories", d:"创建分类" },
        { m:"PUT", p:"/categories/{id}", d:"更新分类" },
        { m:"PUT", p:"/categories/{id}/status", d:"更新分类状态" },
        { m:"DELETE", p:"/categories/{id}", d:"删除分类（会检查是否仍有商品）" },
        { m:"GET", p:"/flowers", d:"商品分页与筛选（categories/statuses/colors/sizes/priceRanges/stockRanges）" },
        { m:"GET", p:"/flowers/{id}", d:"商品详情（含分类名）" },
        { m:"POST", p:"/flowers", d:"创建商品（JSON 体，含图片数组等）" },
        { m:"PUT", p:"/flowers/{id}", d:"更新商品" },
        { m:"PUT", p:"/flowers/{id}/status", d:"更新商品状态" },
        { m:"DELETE", p:"/flowers/{id}", d:"删除商品" },
        { m:"GET", p:"/reviews", d:"评价分页（status/rating 过滤）" },
        { m:"PUT", p:"/reviews/{id}/status", d:"更新评价状态" },
        { m:"DELETE", p:"/reviews/{id}", d:"删除评价" },
        { m:"DELETE", p:"/reviews/batch", d:"批量删除评价（JSON 体：ids[]）" },
        { m:"GET", p:"/provinces", d:"省份列表" },
        { m:"GET", p:"/cities/{provinceCode}", d:"城市列表" },
        { m:"GET", p:"/districts/{cityCode}", d:"区县列表" },
        { m:"GET", p:"/address-stats", d:"地址统计信息" },
        { m:"POST", p:"/provinces", d:"新增省份" },
        { m:"PUT", p:"/provinces/{id}", d:"更新省份" },
        { m:"DELETE", p:"/provinces/{id}", d:"删除省份" },
        { m:"POST", p:"/cities", d:"新增城市" },
        { m:"PUT", p:"/cities/{id}", d:"更新城市" },
        { m:"DELETE", p:"/cities/{id}", d:"删除城市" },
        { m:"POST", p:"/districts", d:"新增区县" },
        { m:"PUT", p:"/districts/{id}", d:"更新区县" },
        { m:"DELETE", p:"/districts/{id}", d:"删除区县" },
        { m:"GET", p:"/price-categories", d:"价格分类分页（keyword/status）" },
        { m:"GET", p:"/price-categories/{id}", d:"价格分类详情" },
        { m:"POST", p:"/price-categories", d:"创建价格分类" },
        { m:"PUT", p:"/price-categories/{id}", d:"更新价格分类" },
        { m:"DELETE", p:"/price-categories/{id}", d:"删除价格分类" },
        { m:"PUT", p:"/price-categories/{id}/status", d:"更新价格分类状态" },
        { m:"GET", p:"/price-categories/active", d:"启用的价格分类（供前台使用）" },
        { m:"GET", p:"/profile", d:"获取当前管理员信息（需 Token）" },
        { m:"PUT", p:"/profile", d:"更新当前管理员信息" },
        { m:"PUT", p:"/password", d:"修改密码" },
        { m:"POST", p:"/upload/avatar", d:"上传管理员头像（multipart）" },
        { m:"GET", p:"/test/avatar/{filename}", d:"测试头像访问" },
      ]},
      { name:"轮播（/swiper）", base:"/swiper", items:[
        { m:"GET", p:"/page", q:"pageNum,pageSize,title,status", d:"轮播分页" },
        { m:"GET", p:"/active", d:"启用的轮播图列表" },
        { m:"GET", p:"/{id}", d:"轮播详情" },
        { m:"POST", p:"", d:"新增轮播（JSON 体）" },
        { m:"PUT", p:"", d:"更新轮播（JSON 体）" },
        { m:"DELETE", p:"/{id}", d:"删除轮播" },
        { m:"DELETE", p:"/batch", d:"批量删除（JSON 体：ids[]）" },
        { m:"POST", p:"/upload", d:"上传轮播图（multipart：file）" },
        { m:"PUT", p:"/status", q:"id,status", d:"更新状态" },
        { m:"PUT", p:"/sort", q:"id,sortOrder", d:"更新排序" },
      ]},
      { name:"文件上传（/admin/upload）", base:"/admin/upload", items:[
        { m:"POST", p:"/image", d:"上传图片（别名，内部转发至 /shop-image）" },
        { m:"POST", p:"/shop-image", d:"上传店铺图片（multipart：file）" },
        { m:"POST", p:"/shop-images", d:"批量上传店铺图片（multipart：files[]，最多10张）" },
        { m:"DELETE", p:"/shop-image", q:"fileName", d:"删除店铺图片" },
      ]},
      { name:"通知（类级路径含 /api）（/api/notification）", base:"/api/notification", items:[
        { m:"POST", p:"/test/new-order", d:"发送测试新订单通知" },
        { m:"POST", p:"/test/order-status", d:"发送测试订单状态通知" },
        { m:"POST", p:"/test/low-stock", d:"发送测试库存预警通知" },
        { m:"GET",  p:"/status", d:"通知在线状态与时间戳" },
      ]},
      { name:"测试（/test）", base:"/test", items:[
        { m:"GET", p:"/db", d:"数据库连通性自检" },
        { m:"GET", p:"/users", d:"快速读取用户" },
        { m:"GET", p:"/flowers", d:"快速读取花卉" },
        { m:"POST", p:"/favorite", q:"userId,flowerId", d:"收藏测试（表单参数）" },
        { m:"POST", p:"/cart", q:"userId,flowerId,quantity", d:"购物车测试（表单参数）" },
      ]},
    ];

    function render() {
      const holder = document.getElementById('endpoints');
      holder.innerHTML = '';
      const kw = (document.getElementById('search').value||'').toLowerCase();
      for (const g of groups) {
        const gDiv = document.createElement('div');
        gDiv.className = 'group';
        const title = document.createElement('div');
        title.innerHTML = `<span class="tag">组</span> <b>${g.name}</b> <span class="sub">前缀：<code class="path">${g.base}</code></span>`;
        gDiv.appendChild(title);
        for (const ep of g.items) {
          const text = `${ep.m} ${g.base}${ep.p} ${ep.d||''} ${ep.q||''}`.toLowerCase();
          if (kw && !text.includes(kw)) continue;
          const det = document.createElement('details');
          det.className = 'endpoint';
          const sum = document.createElement('summary');
          sum.innerHTML = `<span class="method ${ep.m}">${ep.m}</span> <span class="path">${g.base}${ep.p}</span> ${ep.q?`<span class="tag">参数：${ep.q}</span>`:''} <span class="desc">${ep.d||''}</span>`;
          const body = document.createElement('div');
          body.className = 'ep-body';
          const btn = document.createElement('button');
          btn.className = 'btn';
          btn.textContent = '带入测试器';
          btn.onclick = () => fillTester(ep.m, `${g.base}${ep.p}`, ep);
          body.appendChild(btn);
          if (ep.d) { const p = document.createElement('div'); p.className='sub'; p.style.marginTop='8px'; p.textContent = ep.d; body.appendChild(p);}          
          det.appendChild(sum);
          det.appendChild(body);
          gDiv.appendChild(det);
        }
        holder.appendChild(gDiv);
      }
    }

    function fillTester(method, path, ep) {
      document.getElementById('reqMethod').value = method;
      document.getElementById('reqPath').value = path;
      if (method === 'GET' || method === 'DELETE') {
        document.getElementById('reqBody').value = '';
      }
    }

    async function sendRequest() {
      const base = document.getElementById('baseUrl').value.trim().replace(/\/$/, '');
      const method = document.getElementById('reqMethod').value;
      let path = document.getElementById('reqPath').value.trim();
      const headersText = document.getElementById('reqHeaders').value || '{}';
      const commonHeadersText = document.getElementById('commonHeaders').value || '{}';
      let bodyText = document.getElementById('reqBody').value || '';
      try {
        const h1 = JSON.parse(headersText);
        const h2 = JSON.parse(commonHeadersText);
        const headers = { ...h2, ...h1 };
        const url = base + path;
        const init = { method, headers };
        if (method !== 'GET' && method !== 'DELETE') {
          if (headers['Content-Type'] && headers['Content-Type'].includes('application/json')) {
            init.body = bodyText || '{}';
          } else {
            // 允许自定义表单/多部分
            init.body = bodyText;
          }
        }
        const t0 = performance.now();
        const res = await fetch(url, init);
        const t1 = performance.now();
        const ct = res.headers.get('content-type') || '';
        let data;
        try { data = ct.includes('application/json') ? await res.json() : await res.text(); } catch(e) { data = await res.text(); }
        document.getElementById('resultMeta').innerHTML = `状态：<b class="${res.ok?'ok':'err'}">${res.status}</b> · 耗时：${(t1-t0).toFixed(1)} ms · URL：<code>${url}</code>`;
        document.getElementById('result').textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
      } catch (e) {
        document.getElementById('resultMeta').innerHTML = `<span class="err">发送失败</span>`;
        document.getElementById('result').textContent = String(e);
      }
    }

    function clearResult(){ document.getElementById('resultMeta').innerHTML=''; document.getElementById('result').textContent=''; }
    document.getElementById('search').addEventListener('input', render);
    render();
  </script>
</body>
</html>

