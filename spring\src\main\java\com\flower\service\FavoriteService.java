package com.flower.service;

import com.flower.entity.Flower;
import com.flower.entity.UserFavorite;
import java.util.List;

public interface FavoriteService {
  UserFavorite addToFavorites(Long paramLong1, Long paramLong2);
  
  Boolean removeFromFavorites(Long paramLong1, Long paramLong2);
  
  Boolean toggleFavorite(Long paramLong1, Long paramLong2);
  
  Boolean isFavorite(Long paramLong1, Long paramLong2);
  
  List<UserFavorite> getUserFavorites(Long paramLong);
  
  List<Flower> getUserFavoriteFlowers(Long paramLong);
  
  Integer getFavoriteCount(Long paramLong);
}
