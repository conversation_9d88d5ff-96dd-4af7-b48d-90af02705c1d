package com.flower.config;

import java.util.Arrays;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@Configuration
public class CorsConfig {
  @Bean
  public CorsFilter corsFilter() {
    CorsConfiguration config = new CorsConfiguration();
    config.setAllowedOriginPatterns(Arrays.asList(new String[] { "*" }));
    config.addAllowedHeader("*");
    config.setAllowedMethods(Arrays.asList(new String[] { "GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH" }));
    config.setAllowCredentials(Boolean.valueOf(true));
    config.setMaxAge(Long.valueOf(3600L));
    config.setExposedHeaders(Arrays.asList(new String[] { "Access-Control-Allow-Origin", "Access-Control-Allow-Credentials", "Authorization" }));
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", config);
    return new CorsFilter((CorsConfigurationSource)source);
  }
}
