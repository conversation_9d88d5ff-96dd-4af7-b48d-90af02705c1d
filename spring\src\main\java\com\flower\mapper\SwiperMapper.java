package com.flower.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flower.entity.Swiper;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SwiperMapper extends BaseMapper<Swiper> {
  @Select({"SELECT * FROM swiper WHERE status = 1 ORDER BY sort_order ASC, create_time DESC"})
  List<Swiper> getActiveSwipers();
  
  @Select({"SELECT * FROM swiper ORDER BY sort_order ASC, create_time DESC"})
  List<Swiper> getAllSwipers();
}
