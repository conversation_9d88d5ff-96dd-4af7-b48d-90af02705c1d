package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.CartItem;
import com.flower.service.CartService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/cart"})
@CrossOrigin(origins = {"*"})
public class CartController {
  private static final Logger log = LoggerFactory.getLogger(com.flower.controller.CartController.class);
  
  @Autowired
  private CartService cartService;
  
  @PostMapping({"/add"})
  public Result<CartItem> addToCart(@RequestBody Map<String, Object> params) {
    try {
      Long userId = Long.valueOf(params.get("userId").toString());
      Long flowerId = Long.valueOf(params.get("flowerId").toString());
      Integer quantity = Integer.valueOf(params.get("quantity").toString());
      CartItem cartItem = this.cartService.addToCart(userId, flowerId, quantity);
      return Result.success("添加到购物车成功", cartItem);
    } catch (Exception e) {
      log.error("添加到购物车失败", e);
      return Result.error("添加到购物车失败");
    } 
  }
  
  @PutMapping({"/update"})
  public Result<CartItem> updateCartItem(@RequestBody Map<String, Object> params) {
    try {
      Long userId = Long.valueOf(params.get("userId").toString());
      Long flowerId = Long.valueOf(params.get("flowerId").toString());
      Integer quantity = Integer.valueOf(params.get("quantity").toString());
      CartItem cartItem = this.cartService.updateCartItem(userId, flowerId, quantity);
      return Result.success("购物车更新成功", cartItem);
    } catch (Exception e) {
      log.error("更新购物车失败", e);
      return Result.error("更新购物车失败");
    } 
  }
  
  @DeleteMapping({"/remove"})
  public Result<Boolean> removeFromCart(@RequestBody Map<String, Object> params) {
    try {
      Long userId = Long.valueOf(params.get("userId").toString());
      Long flowerId = Long.valueOf(params.get("flowerId").toString());
      Boolean success = this.cartService.removeFromCart(userId, flowerId);
      return Result.success("从购物车移除成功", success);
    } catch (Exception e) {
      log.error("从购物车移除失败", e);
      return Result.error("从购物车移除失败");
    } 
  }
  
  @GetMapping({"/list/{userId}"})
  public Result<List<CartItem>> getCartItems(@PathVariable Long userId) {
    try {
      List<CartItem> cartItems = this.cartService.getUserCartItems(userId);
      return Result.success(cartItems);
    } catch (Exception e) {
      log.error("获取购物车列表失败", e);
      return Result.error("获取购物车列表失败");
    } 
  }
  
  @DeleteMapping({"/batch-remove"})
  public Result<Integer> batchRemoveFromCart(@RequestBody Map<String, Object> params) {
    try {
      Long userId = Long.valueOf(params.get("userId").toString());
      List<Object> flowerIdObjects = (List<Object>)params.get("flowerIds");
      List<Long> flowerIds = (List<Long>)flowerIdObjects.stream().map(obj -> Long.valueOf(obj.toString())).collect(Collectors.toList());
      Integer removedCount = this.cartService.batchRemoveFromCart(userId, flowerIds);
      return Result.success("批量移除成功", removedCount);
    } catch (Exception e) {
      log.error("批量移除失败", e);
      return Result.error("批量移除失败");
    } 
  }
  
  @DeleteMapping({"/clear/{userId}"})
  public Result<Boolean> clearCart(@PathVariable Long userId) {
    try {
      Boolean success = this.cartService.clearCart(userId);
      return Result.success("购物车清空成功", success);
    } catch (Exception e) {
      log.error("清空购物车失败", e);
      return Result.error("清空购物车失败");
    } 
  }
  
  @GetMapping({"/count/{userId}"})
  public Result<Integer> getCartItemCount(@PathVariable Long userId) {
    try {
      Integer count = this.cartService.getCartItemCount(userId);
      return Result.success(count);
    } catch (Exception e) {
      log.error("获取购物车数量失败", e);
      return Result.error("获取购物车数量失败");
    } 
  }
}
