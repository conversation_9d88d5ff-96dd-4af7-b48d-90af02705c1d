package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName("price_categories")
public class PriceCategory {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private String name;
  
  private String description;
  
  private BigDecimal minPrice;
  
  private BigDecimal maxPrice;
  
  private Integer sortOrder;
  
  private Integer status;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public void setDescription(String description) {
    this.description = description;
  }
  
  public void setMinPrice(BigDecimal minPrice) {
    this.minPrice = minPrice;
  }
  
  public void setMaxPrice(BigDecimal maxPrice) {
    this.maxPrice = maxPrice;
  }
  
  public void setSortOrder(Integer sortOrder) {
    this.sortOrder = sortOrder;
  }
  
  public void setStatus(Integer status) {
    this.status = status;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public String toString() {
    return "PriceCategory(id=" + getId() + ", name=" + getName() + ", description=" + getDescription() + ", minPrice=" + getMinPrice() + ", maxPrice=" + getMaxPrice() + ", sortOrder=" + getSortOrder() + ", status=" + getStatus() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.PriceCategory))
      return false; 
    com.flower.entity.PriceCategory other = (com.flower.entity.PriceCategory)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$sortOrder = getSortOrder(), other$sortOrder = other.getSortOrder();
    if ((this$sortOrder == null) ? (other$sortOrder != null) : !this$sortOrder.equals(other$sortOrder))
      return false; 
    Object this$status = getStatus(), other$status = other.getStatus();
    if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status))
      return false; 
    Object this$name = getName(), other$name = other.getName();
    if ((this$name == null) ? (other$name != null) : !this$name.equals(other$name))
      return false; 
    Object this$description = getDescription(), other$description = other.getDescription();
    if ((this$description == null) ? (other$description != null) : !this$description.equals(other$description))
      return false; 
    Object this$minPrice = getMinPrice(), other$minPrice = other.getMinPrice();
    if ((this$minPrice == null) ? (other$minPrice != null) : !this$minPrice.equals(other$minPrice))
      return false; 
    Object this$maxPrice = getMaxPrice(), other$maxPrice = other.getMaxPrice();
    if ((this$maxPrice == null) ? (other$maxPrice != null) : !this$maxPrice.equals(other$maxPrice))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.PriceCategory;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $sortOrder = getSortOrder();
    result = result * 59 + (($sortOrder == null) ? 43 : $sortOrder.hashCode());
    Object $status = getStatus();
    result = result * 59 + (($status == null) ? 43 : $status.hashCode());
    Object $name = getName();
    result = result * 59 + (($name == null) ? 43 : $name.hashCode());
    Object $description = getDescription();
    result = result * 59 + (($description == null) ? 43 : $description.hashCode());
    Object $minPrice = getMinPrice();
    result = result * 59 + (($minPrice == null) ? 43 : $minPrice.hashCode());
    Object $maxPrice = getMaxPrice();
    result = result * 59 + (($maxPrice == null) ? 43 : $maxPrice.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public String getName() {
    return this.name;
  }
  
  public String getDescription() {
    return this.description;
  }
  
  public BigDecimal getMinPrice() {
    return this.minPrice;
  }
  
  public BigDecimal getMaxPrice() {
    return this.maxPrice;
  }
  
  public Integer getSortOrder() {
    return this.sortOrder;
  }
  
  public Integer getStatus() {
    return this.status;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
