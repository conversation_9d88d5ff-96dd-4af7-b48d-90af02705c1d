package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

@TableName("admin_logs")
public class AdminLog {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private Long adminId;
  
  private String adminUsername;
  
  private String action;
  
  private String resource;
  
  private String resourceId;
  
  private String description;
  
  private String ipAddress;
  
  private String userAgent;
  
  private LocalDateTime createdAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setAdminId(Long adminId) {
    this.adminId = adminId;
  }
  
  public void setAdminUsername(String adminUsername) {
    this.adminUsername = adminUsername;
  }
  
  public void setAction(String action) {
    this.action = action;
  }
  
  public void setResource(String resource) {
    this.resource = resource;
  }
  
  public void setResourceId(String resourceId) {
    this.resourceId = resourceId;
  }
  
  public void setDescription(String description) {
    this.description = description;
  }
  
  public void setIpAddress(String ipAddress) {
    this.ipAddress = ipAddress;
  }
  
  public void setUserAgent(String userAgent) {
    this.userAgent = userAgent;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public String toString() {
    return "AdminLog(id=" + getId() + ", adminId=" + getAdminId() + ", adminUsername=" + getAdminUsername() + ", action=" + getAction() + ", resource=" + getResource() + ", resourceId=" + getResourceId() + ", description=" + getDescription() + ", ipAddress=" + getIpAddress() + ", userAgent=" + getUserAgent() + ", createdAt=" + getCreatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.AdminLog))
      return false; 
    com.flower.entity.AdminLog other = (com.flower.entity.AdminLog)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$adminId = getAdminId(), other$adminId = other.getAdminId();
    if ((this$adminId == null) ? (other$adminId != null) : !this$adminId.equals(other$adminId))
      return false; 
    Object this$adminUsername = getAdminUsername(), other$adminUsername = other.getAdminUsername();
    if ((this$adminUsername == null) ? (other$adminUsername != null) : !this$adminUsername.equals(other$adminUsername))
      return false; 
    Object this$action = getAction(), other$action = other.getAction();
    if ((this$action == null) ? (other$action != null) : !this$action.equals(other$action))
      return false; 
    Object this$resource = getResource(), other$resource = other.getResource();
    if ((this$resource == null) ? (other$resource != null) : !this$resource.equals(other$resource))
      return false; 
    Object this$resourceId = getResourceId(), other$resourceId = other.getResourceId();
    if ((this$resourceId == null) ? (other$resourceId != null) : !this$resourceId.equals(other$resourceId))
      return false; 
    Object this$description = getDescription(), other$description = other.getDescription();
    if ((this$description == null) ? (other$description != null) : !this$description.equals(other$description))
      return false; 
    Object this$ipAddress = getIpAddress(), other$ipAddress = other.getIpAddress();
    if ((this$ipAddress == null) ? (other$ipAddress != null) : !this$ipAddress.equals(other$ipAddress))
      return false; 
    Object this$userAgent = getUserAgent(), other$userAgent = other.getUserAgent();
    if ((this$userAgent == null) ? (other$userAgent != null) : !this$userAgent.equals(other$userAgent))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    return !((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.AdminLog;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $adminId = getAdminId();
    result = result * 59 + (($adminId == null) ? 43 : $adminId.hashCode());
    Object $adminUsername = getAdminUsername();
    result = result * 59 + (($adminUsername == null) ? 43 : $adminUsername.hashCode());
    Object $action = getAction();
    result = result * 59 + (($action == null) ? 43 : $action.hashCode());
    Object $resource = getResource();
    result = result * 59 + (($resource == null) ? 43 : $resource.hashCode());
    Object $resourceId = getResourceId();
    result = result * 59 + (($resourceId == null) ? 43 : $resourceId.hashCode());
    Object $description = getDescription();
    result = result * 59 + (($description == null) ? 43 : $description.hashCode());
    Object $ipAddress = getIpAddress();
    result = result * 59 + (($ipAddress == null) ? 43 : $ipAddress.hashCode());
    Object $userAgent = getUserAgent();
    result = result * 59 + (($userAgent == null) ? 43 : $userAgent.hashCode());
    Object $createdAt = getCreatedAt();
    return result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public Long getAdminId() {
    return this.adminId;
  }
  
  public String getAdminUsername() {
    return this.adminUsername;
  }
  
  public String getAction() {
    return this.action;
  }
  
  public String getResource() {
    return this.resource;
  }
  
  public String getResourceId() {
    return this.resourceId;
  }
  
  public String getDescription() {
    return this.description;
  }
  
  public String getIpAddress() {
    return this.ipAddress;
  }
  
  public String getUserAgent() {
    return this.userAgent;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
}
