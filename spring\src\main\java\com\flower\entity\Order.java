package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName("orders")
public class Order {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private String orderNo;
  
  private Long userId;
  
  private BigDecimal totalAmount;
  
  private BigDecimal discountAmount;
  
  private BigDecimal finalAmount;
  
  private Integer status;
  
  private Integer deliveryStatus;
  
  private String paymentMethod;
  
  private Integer paymentStatus;
  
  private String recipientName;
  
  private String recipientPhone;
  
  private String recipientAddress;
  
  private String deliveryNotes;
  
  private String remark;
  
  private Integer deliveryType;
  
  private String pickupName;
  
  private String pickupPhone;
  
  private String pickupTime;
  
  @TableField("delivery_time")
  private String deliveryTime;
  
  private LocalDateTime paidAt;
  
  private LocalDateTime shippedAt;
  
  private LocalDateTime arrivedAt;
  
  private LocalDateTime deliveredAt;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
  }
  
  public void setUserId(Long userId) {
    this.userId = userId;
  }
  
  public void setTotalAmount(BigDecimal totalAmount) {
    this.totalAmount = totalAmount;
  }
  
  public void setDiscountAmount(BigDecimal discountAmount) {
    this.discountAmount = discountAmount;
  }
  
  public void setFinalAmount(BigDecimal finalAmount) {
    this.finalAmount = finalAmount;
  }
  
  public void setStatus(Integer status) {
    this.status = status;
  }
  
  public void setDeliveryStatus(Integer deliveryStatus) {
    this.deliveryStatus = deliveryStatus;
  }
  
  public void setPaymentMethod(String paymentMethod) {
    this.paymentMethod = paymentMethod;
  }
  
  public void setPaymentStatus(Integer paymentStatus) {
    this.paymentStatus = paymentStatus;
  }
  
  public void setRecipientName(String recipientName) {
    this.recipientName = recipientName;
  }
  
  public void setRecipientPhone(String recipientPhone) {
    this.recipientPhone = recipientPhone;
  }
  
  public void setRecipientAddress(String recipientAddress) {
    this.recipientAddress = recipientAddress;
  }
  
  public void setDeliveryNotes(String deliveryNotes) {
    this.deliveryNotes = deliveryNotes;
  }
  
  public void setRemark(String remark) {
    this.remark = remark;
  }
  
  public void setDeliveryType(Integer deliveryType) {
    this.deliveryType = deliveryType;
  }
  
  public void setPickupName(String pickupName) {
    this.pickupName = pickupName;
  }
  
  public void setPickupPhone(String pickupPhone) {
    this.pickupPhone = pickupPhone;
  }
  
  public void setPickupTime(String pickupTime) {
    this.pickupTime = pickupTime;
  }
  
  public void setDeliveryTime(String deliveryTime) {
    this.deliveryTime = deliveryTime;
  }
  
  public void setPaidAt(LocalDateTime paidAt) {
    this.paidAt = paidAt;
  }
  
  public void setShippedAt(LocalDateTime shippedAt) {
    this.shippedAt = shippedAt;
  }
  
  public void setArrivedAt(LocalDateTime arrivedAt) {
    this.arrivedAt = arrivedAt;
  }
  
  public void setDeliveredAt(LocalDateTime deliveredAt) {
    this.deliveredAt = deliveredAt;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public String toString() {
    return "Order(id=" + getId() + ", orderNo=" + getOrderNo() + ", userId=" + getUserId() + ", totalAmount=" + getTotalAmount() + ", discountAmount=" + getDiscountAmount() + ", finalAmount=" + getFinalAmount() + ", status=" + getStatus() + ", deliveryStatus=" + getDeliveryStatus() + ", paymentMethod=" + getPaymentMethod() + ", paymentStatus=" + getPaymentStatus() + ", recipientName=" + getRecipientName() + ", recipientPhone=" + getRecipientPhone() + ", recipientAddress=" + getRecipientAddress() + ", deliveryNotes=" + getDeliveryNotes() + ", remark=" + getRemark() + ", deliveryType=" + getDeliveryType() + ", pickupName=" + getPickupName() + ", pickupPhone=" + getPickupPhone() + ", pickupTime=" + getPickupTime() + ", deliveryTime=" + getDeliveryTime() + ", paidAt=" + getPaidAt() + ", shippedAt=" + getShippedAt() + ", arrivedAt=" + getArrivedAt() + ", deliveredAt=" + getDeliveredAt() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.Order))
      return false; 
    com.flower.entity.Order other = (com.flower.entity.Order)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$userId = getUserId(), other$userId = other.getUserId();
    if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId))
      return false; 
    Object this$status = getStatus(), other$status = other.getStatus();
    if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status))
      return false; 
    Object this$deliveryStatus = getDeliveryStatus(), other$deliveryStatus = other.getDeliveryStatus();
    if ((this$deliveryStatus == null) ? (other$deliveryStatus != null) : !this$deliveryStatus.equals(other$deliveryStatus))
      return false; 
    Object this$paymentStatus = getPaymentStatus(), other$paymentStatus = other.getPaymentStatus();
    if ((this$paymentStatus == null) ? (other$paymentStatus != null) : !this$paymentStatus.equals(other$paymentStatus))
      return false; 
    Object this$deliveryType = getDeliveryType(), other$deliveryType = other.getDeliveryType();
    if ((this$deliveryType == null) ? (other$deliveryType != null) : !this$deliveryType.equals(other$deliveryType))
      return false; 
    Object this$orderNo = getOrderNo(), other$orderNo = other.getOrderNo();
    if ((this$orderNo == null) ? (other$orderNo != null) : !this$orderNo.equals(other$orderNo))
      return false; 
    Object this$totalAmount = getTotalAmount(), other$totalAmount = other.getTotalAmount();
    if ((this$totalAmount == null) ? (other$totalAmount != null) : !this$totalAmount.equals(other$totalAmount))
      return false; 
    Object this$discountAmount = getDiscountAmount(), other$discountAmount = other.getDiscountAmount();
    if ((this$discountAmount == null) ? (other$discountAmount != null) : !this$discountAmount.equals(other$discountAmount))
      return false; 
    Object this$finalAmount = getFinalAmount(), other$finalAmount = other.getFinalAmount();
    if ((this$finalAmount == null) ? (other$finalAmount != null) : !this$finalAmount.equals(other$finalAmount))
      return false; 
    Object this$paymentMethod = getPaymentMethod(), other$paymentMethod = other.getPaymentMethod();
    if ((this$paymentMethod == null) ? (other$paymentMethod != null) : !this$paymentMethod.equals(other$paymentMethod))
      return false; 
    Object this$recipientName = getRecipientName(), other$recipientName = other.getRecipientName();
    if ((this$recipientName == null) ? (other$recipientName != null) : !this$recipientName.equals(other$recipientName))
      return false; 
    Object this$recipientPhone = getRecipientPhone(), other$recipientPhone = other.getRecipientPhone();
    if ((this$recipientPhone == null) ? (other$recipientPhone != null) : !this$recipientPhone.equals(other$recipientPhone))
      return false; 
    Object this$recipientAddress = getRecipientAddress(), other$recipientAddress = other.getRecipientAddress();
    if ((this$recipientAddress == null) ? (other$recipientAddress != null) : !this$recipientAddress.equals(other$recipientAddress))
      return false; 
    Object this$deliveryNotes = getDeliveryNotes(), other$deliveryNotes = other.getDeliveryNotes();
    if ((this$deliveryNotes == null) ? (other$deliveryNotes != null) : !this$deliveryNotes.equals(other$deliveryNotes))
      return false; 
    Object this$remark = getRemark(), other$remark = other.getRemark();
    if ((this$remark == null) ? (other$remark != null) : !this$remark.equals(other$remark))
      return false; 
    Object this$pickupName = getPickupName(), other$pickupName = other.getPickupName();
    if ((this$pickupName == null) ? (other$pickupName != null) : !this$pickupName.equals(other$pickupName))
      return false; 
    Object this$pickupPhone = getPickupPhone(), other$pickupPhone = other.getPickupPhone();
    if ((this$pickupPhone == null) ? (other$pickupPhone != null) : !this$pickupPhone.equals(other$pickupPhone))
      return false; 
    Object this$pickupTime = getPickupTime(), other$pickupTime = other.getPickupTime();
    if ((this$pickupTime == null) ? (other$pickupTime != null) : !this$pickupTime.equals(other$pickupTime))
      return false; 
    Object this$deliveryTime = getDeliveryTime(), other$deliveryTime = other.getDeliveryTime();
    if ((this$deliveryTime == null) ? (other$deliveryTime != null) : !this$deliveryTime.equals(other$deliveryTime))
      return false; 
    Object this$paidAt = getPaidAt(), other$paidAt = other.getPaidAt();
    if ((this$paidAt == null) ? (other$paidAt != null) : !this$paidAt.equals(other$paidAt))
      return false; 
    Object this$shippedAt = getShippedAt(), other$shippedAt = other.getShippedAt();
    if ((this$shippedAt == null) ? (other$shippedAt != null) : !this$shippedAt.equals(other$shippedAt))
      return false; 
    Object this$arrivedAt = getArrivedAt(), other$arrivedAt = other.getArrivedAt();
    if ((this$arrivedAt == null) ? (other$arrivedAt != null) : !this$arrivedAt.equals(other$arrivedAt))
      return false; 
    Object this$deliveredAt = getDeliveredAt(), other$deliveredAt = other.getDeliveredAt();
    if ((this$deliveredAt == null) ? (other$deliveredAt != null) : !this$deliveredAt.equals(other$deliveredAt))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.Order;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $userId = getUserId();
    result = result * 59 + (($userId == null) ? 43 : $userId.hashCode());
    Object $status = getStatus();
    result = result * 59 + (($status == null) ? 43 : $status.hashCode());
    Object $deliveryStatus = getDeliveryStatus();
    result = result * 59 + (($deliveryStatus == null) ? 43 : $deliveryStatus.hashCode());
    Object $paymentStatus = getPaymentStatus();
    result = result * 59 + (($paymentStatus == null) ? 43 : $paymentStatus.hashCode());
    Object $deliveryType = getDeliveryType();
    result = result * 59 + (($deliveryType == null) ? 43 : $deliveryType.hashCode());
    Object $orderNo = getOrderNo();
    result = result * 59 + (($orderNo == null) ? 43 : $orderNo.hashCode());
    Object $totalAmount = getTotalAmount();
    result = result * 59 + (($totalAmount == null) ? 43 : $totalAmount.hashCode());
    Object $discountAmount = getDiscountAmount();
    result = result * 59 + (($discountAmount == null) ? 43 : $discountAmount.hashCode());
    Object $finalAmount = getFinalAmount();
    result = result * 59 + (($finalAmount == null) ? 43 : $finalAmount.hashCode());
    Object $paymentMethod = getPaymentMethod();
    result = result * 59 + (($paymentMethod == null) ? 43 : $paymentMethod.hashCode());
    Object $recipientName = getRecipientName();
    result = result * 59 + (($recipientName == null) ? 43 : $recipientName.hashCode());
    Object $recipientPhone = getRecipientPhone();
    result = result * 59 + (($recipientPhone == null) ? 43 : $recipientPhone.hashCode());
    Object $recipientAddress = getRecipientAddress();
    result = result * 59 + (($recipientAddress == null) ? 43 : $recipientAddress.hashCode());
    Object $deliveryNotes = getDeliveryNotes();
    result = result * 59 + (($deliveryNotes == null) ? 43 : $deliveryNotes.hashCode());
    Object $remark = getRemark();
    result = result * 59 + (($remark == null) ? 43 : $remark.hashCode());
    Object $pickupName = getPickupName();
    result = result * 59 + (($pickupName == null) ? 43 : $pickupName.hashCode());
    Object $pickupPhone = getPickupPhone();
    result = result * 59 + (($pickupPhone == null) ? 43 : $pickupPhone.hashCode());
    Object $pickupTime = getPickupTime();
    result = result * 59 + (($pickupTime == null) ? 43 : $pickupTime.hashCode());
    Object $deliveryTime = getDeliveryTime();
    result = result * 59 + (($deliveryTime == null) ? 43 : $deliveryTime.hashCode());
    Object $paidAt = getPaidAt();
    result = result * 59 + (($paidAt == null) ? 43 : $paidAt.hashCode());
    Object $shippedAt = getShippedAt();
    result = result * 59 + (($shippedAt == null) ? 43 : $shippedAt.hashCode());
    Object $arrivedAt = getArrivedAt();
    result = result * 59 + (($arrivedAt == null) ? 43 : $arrivedAt.hashCode());
    Object $deliveredAt = getDeliveredAt();
    result = result * 59 + (($deliveredAt == null) ? 43 : $deliveredAt.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public String getOrderNo() {
    return this.orderNo;
  }
  
  public Long getUserId() {
    return this.userId;
  }
  
  public BigDecimal getTotalAmount() {
    return this.totalAmount;
  }
  
  public BigDecimal getDiscountAmount() {
    return this.discountAmount;
  }
  
  public BigDecimal getFinalAmount() {
    return this.finalAmount;
  }
  
  public Integer getStatus() {
    return this.status;
  }
  
  public Integer getDeliveryStatus() {
    return this.deliveryStatus;
  }
  
  public String getPaymentMethod() {
    return this.paymentMethod;
  }
  
  public Integer getPaymentStatus() {
    return this.paymentStatus;
  }
  
  public String getRecipientName() {
    return this.recipientName;
  }
  
  public String getRecipientPhone() {
    return this.recipientPhone;
  }
  
  public String getRecipientAddress() {
    return this.recipientAddress;
  }
  
  public String getDeliveryNotes() {
    return this.deliveryNotes;
  }
  
  public String getRemark() {
    return this.remark;
  }
  
  public Integer getDeliveryType() {
    return this.deliveryType;
  }
  
  public String getPickupName() {
    return this.pickupName;
  }
  
  public String getPickupPhone() {
    return this.pickupPhone;
  }
  
  public String getPickupTime() {
    return this.pickupTime;
  }
  
  public String getDeliveryTime() {
    return this.deliveryTime;
  }
  
  public LocalDateTime getPaidAt() {
    return this.paidAt;
  }
  
  public LocalDateTime getShippedAt() {
    return this.shippedAt;
  }
  
  public LocalDateTime getArrivedAt() {
    return this.arrivedAt;
  }
  
  public LocalDateTime getDeliveredAt() {
    return this.deliveredAt;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
