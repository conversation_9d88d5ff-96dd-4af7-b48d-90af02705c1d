package com.flower.common;

import java.util.List;

public class PageResult<T> {
  private List<T> records;
  
  private Long total;
  
  private Long size;
  
  private Long current;
  
  private Long pages;
  
  public void setRecords(List<T> records) {
    this.records = records;
  }
  
  public void setTotal(Long total) {
    this.total = total;
  }
  
  public void setSize(Long size) {
    this.size = size;
  }
  
  public void setCurrent(Long current) {
    this.current = current;
  }
  
  public void setPages(Long pages) {
    this.pages = pages;
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.common.PageResult))
      return false; 
    com.flower.common.PageResult<?> other = (com.flower.common.PageResult)o;
    if (!other.canEqual(this))
      return false; 
    Object this$total = getTotal(), other$total = other.getTotal();
    if ((this$total == null) ? (other$total != null) : !this$total.equals(other$total))
      return false; 
    Object this$size = getSize(), other$size = other.getSize();
    if ((this$size == null) ? (other$size != null) : !this$size.equals(other$size))
      return false; 
    Object this$current = getCurrent(), other$current = other.getCurrent();
    if ((this$current == null) ? (other$current != null) : !this$current.equals(other$current))
      return false; 
    Object this$pages = getPages(), other$pages = other.getPages();
    if ((this$pages == null) ? (other$pages != null) : !this$pages.equals(other$pages))
      return false; 
    java.util.List<?> this$records = getRecords();
    java.util.List<?> other$records = other.getRecords();
    return !((this$records == null) ? (other$records != null) : !this$records.equals(other$records));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.common.PageResult;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;;
    Object $total = getTotal();
    result = result * 59 + (($total == null) ? 43 : $total.hashCode());
    Object $size = getSize();
    result = result * 59 + (($size == null) ? 43 : $size.hashCode());
    Object $current = getCurrent();
    result = result * 59 + (($current == null) ? 43 : $current.hashCode());
    Object $pages = getPages();
    result = result * 59 + (($pages == null) ? 43 : $pages.hashCode());
    java.util.List<?> $records = getRecords();
    return result * 59 + (($records == null) ? 43 : $records.hashCode());
  }
  
  public String toString() {
    return "PageResult(records=" + getRecords() + ", total=" + getTotal() + ", size=" + getSize() + ", current=" + getCurrent() + ", pages=" + getPages() + ")";
  }
  
  public List<T> getRecords() {
    return this.records;
  }
  
  public Long getTotal() {
    return this.total;
  }
  
  public Long getSize() {
    return this.size;
  }
  
  public Long getCurrent() {
    return this.current;
  }
  
  public Long getPages() {
    return this.pages;
  }
  
  public PageResult() {}
  
  public PageResult(List<T> records, Long total, Long size, Long current) {
    this.records = records;
    this.total = total;
    this.size = size;
    this.current = current;
    this.pages = Long.valueOf((total.longValue() + size.longValue() - 1L) / size.longValue());
  }
  
  public static <T> com.flower.common.PageResult<T> of(List<T> records, Long total, Long size, Long current) {
    return new com.flower.common.PageResult<>(records, total, size, current);
  }
}
