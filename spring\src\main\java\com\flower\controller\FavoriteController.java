package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.Flower;
import com.flower.entity.UserFavorite;
import com.flower.service.FavoriteService;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/favorite"})
@CrossOrigin(origins = {"*"})
public class FavoriteController {
  private static final Logger log = LoggerFactory.getLogger(com.flower.controller.FavoriteController.class);
  
  @Autowired
  private FavoriteService favoriteService;
  
  @PostMapping({"/add"})
  public Result<UserFavorite> addToFavorites(@RequestBody Map<String, Object> params) {
    try {
      Long userId = Long.valueOf(params.get("userId").toString());
      Long flowerId = Long.valueOf(params.get("flowerId").toString());
      UserFavorite favorite = this.favoriteService.addToFavorites(userId, flowerId);
      if (favorite == null)
        return Result.error("已在收藏列表中"); 
      return Result.success("添加收藏成功", favorite);
    } catch (Exception e) {
      log.error("添加收藏失败", e);
      return Result.error("添加收藏失败");
    } 
  }
  
  @DeleteMapping({"/remove"})
  public Result<Boolean> removeFromFavorites(@RequestBody Map<String, Object> params) {
    try {
      Long userId = Long.valueOf(params.get("userId").toString());
      Long flowerId = Long.valueOf(params.get("flowerId").toString());
      Boolean success = this.favoriteService.removeFromFavorites(userId, flowerId);
      return Result.success("取消收藏成功", success);
    } catch (Exception e) {
      log.error("取消收藏失败", e);
      return Result.error("取消收藏失败");
    } 
  }
  
  @GetMapping({"/check"})
  public Result<Boolean> isFavorite(@RequestParam Long userId, @RequestParam Long flowerId) {
    try {
      Boolean isFavorite = this.favoriteService.isFavorite(userId, flowerId);
      return Result.success(isFavorite);
    } catch (Exception e) {
      log.error("检查收藏状态失败", e);
      return Result.error("检查收藏状态失败");
    } 
  }
  
  @PostMapping({"/toggle"})
  public Result<Boolean> toggleFavorite(@RequestBody Map<String, Object> params) {
    try {
      Long userId = Long.valueOf(params.get("userId").toString());
      Long flowerId = Long.valueOf(params.get("flowerId").toString());
      Boolean isFavorite = this.favoriteService.toggleFavorite(userId, flowerId);
      String message = isFavorite.booleanValue() ? "添加收藏成功" : "取消收藏成功";
      return Result.success(message, isFavorite);
    } catch (Exception e) {
      log.error("切换收藏状态失败", e);
      return Result.error("操作失败");
    } 
  }
  
  @GetMapping({"/list/{userId}"})
  public Result<List<UserFavorite>> getUserFavorites(@PathVariable Long userId) {
    try {
      List<UserFavorite> favorites = this.favoriteService.getUserFavorites(userId);
      return Result.success(favorites);
    } catch (Exception e) {
      log.error("获取收藏列表失败", e);
      return Result.error("获取收藏列表失败");
    } 
  }
  
  @GetMapping({"/flowers/{userId}"})
  public Result<List<Flower>> getUserFavoriteFlowers(@PathVariable Long userId) {
    try {
      List<Flower> flowers = this.favoriteService.getUserFavoriteFlowers(userId);
      return Result.success(flowers);
    } catch (Exception e) {
      log.error("获取收藏花卉列表失败", e);
      return Result.error("获取收藏花卉列表失败");
    } 
  }
  
  @GetMapping({"/count/{userId}"})
  public Result<Integer> getFavoriteCount(@PathVariable Long userId) {
    try {
      Integer count = this.favoriteService.getFavoriteCount(userId);
      return Result.success(count);
    } catch (Exception e) {
      log.error("获取收藏数量失败", e);
      return Result.error("获取收藏数量失败");
    } 
  }
}
