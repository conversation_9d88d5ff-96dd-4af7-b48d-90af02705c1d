package com.flower.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
  public void addResourceHandlers(ResourceHandlerRegistry registry) {
    String jarPath = System.getProperty("user.dir");
    String userImagePath = "file:" + jarPath + "/image/user-image/";
    System.out.println("配置用户头像静态资源路径: " + userImagePath);
    String shopImagePath = "file:" + jarPath + "/image/shop-image/";
    System.out.println("配置商品图片静态资源路径: " + shopImagePath);
    String adminImagePath = "file:" + jarPath + "/image/admin-image/";
    System.out.println("配置管理员头像静态资源路径: " + adminImagePath);
    String swiperImagePath = "file:" + jarPath + "/image/swiper/";
    System.out.println("配置轮播图静态资源路径: " + swiperImagePath);
    registry.addResourceHandler(new String[] { "/image/flower/**" }).addResourceLocations(new String[] { shopImagePath }).addResourceLocations(new String[] { "classpath:/image/shop-image/" });
    registry.addResourceHandler(new String[] { "/image/user-image/**" }).addResourceLocations(new String[] { userImagePath }).addResourceLocations(new String[] { "classpath:/image/user-image/" });
    registry.addResourceHandler(new String[] { "/image/shop-image/**" }).addResourceLocations(new String[] { shopImagePath }).addResourceLocations(new String[] { "classpath:/image/shop-image/" });
    registry.addResourceHandler(new String[] { "/image/admin-image/**" }).addResourceLocations(new String[] { adminImagePath }).addResourceLocations(new String[] { "classpath:/image/admin-image/" });
    registry.addResourceHandler(new String[] { "/image/swiper/**" }).addResourceLocations(new String[] { swiperImagePath }).addResourceLocations(new String[] { "classpath:/image/swiper/" });
  }
}
