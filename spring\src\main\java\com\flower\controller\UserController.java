package com.flower.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.flower.common.Result;
import com.flower.config.WeChatConfig;
import com.flower.entity.User;
import com.flower.service.UserService;
import com.flower.util.WeChatDecryptUtil;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping({"/user"})
@CrossOrigin(origins = {"*"})
public class UserController {
  private static final Logger log = LoggerFactory.getLogger(com.flower.controller.UserController.class);
  
  @Autowired
  private UserService userService;
  
  @Autowired
  private WeChatConfig weChatConfig;
  
  @PostMapping({"/login"})
  public Result<User> login(@RequestBody Map<String, String> params) {
    try {
      String code = params.get("code");
      if (code == null || code.trim().isEmpty())
        return Result.paramError("登录凭证不能为空"); 
      User user = this.userService.wechatLogin(code);
      return Result.success("登录成功", user);
    } catch (Exception e) {
      log.error("登录失败", e);
      return Result.error("登录失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/info/{openid}"})
  public Result<User> getUserInfo(@PathVariable String openid) {
    try {
      User user = this.userService.getUserByOpenid(openid);
      if (user == null)
        return Result.notFound("用户不存在"); 
      return Result.success(user);
    } catch (Exception e) {
      log.error("获取用户信息失败", e);
      return Result.error("获取用户信息失败");
    } 
  }
  
  @PutMapping({"/update"})
  public Result<User> updateUser(@RequestBody User user) {
    try {
      if (user.getId() == null)
        return Result.paramError("用户ID不能为空"); 
      User updatedUser = this.userService.updateUser(user);
      return Result.success("用户信息更新成功", updatedUser);
    } catch (Exception e) {
      log.error("更新用户信息失败", e);
      return Result.error("更新用户信息失败");
    } 
  }
  
  @PostMapping({"/upload-avatar"})
  public Result<Map<String, String>> uploadAvatar(@RequestParam("file") MultipartFile file, @RequestParam("userId") Long userId) {
    try {
      if (file.isEmpty())
        return Result.paramError("头像文件不能为空"); 
      if (userId == null)
        return Result.paramError("用户ID不能为空"); 
      String contentType = file.getContentType();
      if (contentType == null || !contentType.startsWith("image/"))
        return Result.paramError("只支持图片格式的头像"); 
      if (file.getSize() > 5242880L)
        return Result.paramError("头像文件大小不能超过5MB"); 
      String avatarUrl = this.userService.uploadAvatar(file, userId);
      Map<String, String> result = new HashMap<>();
      result.put("avatarUrl", avatarUrl);
      return Result.success("头像上传成功", result);
    } catch (Exception e) {
      log.error("头像上传失败", e);
      return Result.error("头像上传失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/update-profile"})
  public Result<User> updateUserProfile(@RequestBody Map<String, Object> params) {
    try {
      Long userId = Long.valueOf(params.get("userId").toString());
      String nickname = (String)params.get("nickname");
      String avatarBase64 = (String)params.get("avatarBase64");
      String phone = (String)params.get("phone");
      if (userId == null)
        return Result.paramError("用户ID不能为空"); 
      User updatedUser = this.userService.updateUserProfile(userId, nickname, avatarBase64, phone);
      return Result.success("用户信息更新成功", updatedUser);
    } catch (Exception e) {
      log.error("更新用户信息失败", e);
      return Result.error("更新用户信息失败: " + e.getMessage());
    } 
  }
  
  @PutMapping({"/phone"})
  public Result<User> updatePhone(@RequestBody Map<String, Object> params) {
    try {
      Long userId = Long.valueOf(params.get("userId").toString());
      String phone = params.get("phone").toString();
      if (phone == null || phone.trim().isEmpty())
        return Result.paramError("手机号不能为空"); 
      User user = this.userService.updateUserPhone(userId, phone);
      return Result.success("手机号更新成功", user);
    } catch (Exception e) {
      log.error("更新手机号失败", e);
      return Result.error("更新手机号失败");
    } 
  }
  
  @PostMapping({"/phone"})
  public Result<Map<String, Object>> getPhoneNumber(@RequestBody Map<String, Object> request) {
    try {
      Long userId = Long.valueOf(request.get("userId").toString());
      String code = (String)request.get("code");
      String encryptedData = (String)request.get("encryptedData");
      String iv = (String)request.get("iv");
      log.info("开始获取手机号，userId: {}, code: {}", userId, code);
      User user = this.userService.findById(userId);
      if (user == null)
        return Result.error("用户不存在"); 
      if (code != null && !code.isEmpty() && !"mock_code".equals(code))
        try {
          Map<String, Object> phoneResult = getPhoneNumberByCode(code);
          if (phoneResult != null) {
            String phoneNumber = (String)phoneResult.get("purePhoneNumber");
            user.setPhone(phoneNumber);
            this.userService.save(user);
            log.info("用户手机号获取成功（新版API），userId: {}, phone: {}", userId, phoneNumber);
            return Result.success("手机号获取成功", phoneResult);
          } 
        } catch (Exception e) {
          log.error("新版API获取手机号失败，尝试旧版方式，userId: {}", userId, e);
        }  
      if (encryptedData != null && iv != null) {
        String sessionKey = user.getSessionKey();
        if ("mock_encrypted_data".equals(encryptedData) || "mock_iv".equals(iv) || sessionKey == null || sessionKey
          .isEmpty() || sessionKey.startsWith("mock_")) {
          log.warn("检测到模拟请求或sessionKey为空，使用模拟数据，userId: {}", userId);
          return generateMockPhoneResult(user);
        } 
        try {
          JSONObject phoneInfo = WeChatDecryptUtil.decryptPhoneNumber(encryptedData, sessionKey, iv);
          Map<String, Object> result = new HashMap<>();
          result.put("phoneNumber", phoneInfo.getString("phoneNumber"));
          result.put("purePhoneNumber", phoneInfo.getString("purePhoneNumber"));
          result.put("countryCode", phoneInfo.getString("countryCode"));
          user.setPhone(phoneInfo.getString("purePhoneNumber"));
          this.userService.save(user);
          log.info("用户手机号解密并更新成功（旧版API），userId: {}, phone: {}", userId, phoneInfo.getString("purePhoneNumber"));
          return Result.success("手机号获取成功", result);
        } catch (Exception decryptError) {
          log.error("手机号解密失败，使用模拟数据，userId: {}", userId, decryptError);
          return generateMockPhoneResult(user);
        } 
      } 
      log.warn("未提供有效的手机号获取参数，使用模拟数据，userId: {}", userId);
      return generateMockPhoneResult(user);
    } catch (Exception e) {
      log.error("获取手机号接口异常", e);
      return Result.error("手机号获取失败: " + e.getMessage());
    } 
  }
  
  @GetMapping({"/config"})
  public Result<String> checkConfig() {
    return Result.success("配置检查完成");
  }
  
  private Map<String, Object> getPhoneNumberByCode(String code) {
    try {
      String accessToken = getAccessToken();
      if (accessToken == null) {
        log.error("获取access_token失败");
        return null;
      } 
      String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken;
      Map<String, String> requestData = new HashMap<>();
      requestData.put("code", code);
      String response = sendHttpPostRequest(url, requestData);
      if (response == null) {
        log.error("调用微信API失败");
        return null;
      } 
      JSONObject responseJson = JSON.parseObject(response);
      if (responseJson.getInteger("errcode").intValue() != 0) {
        log.error("微信API返回错误: {}", responseJson.getString("errmsg"));
        return null;
      } 
      JSONObject phoneInfo = responseJson.getJSONObject("phone_info");
      Map<String, Object> result = new HashMap<>();
      result.put("phoneNumber", phoneInfo.getString("phoneNumber"));
      result.put("purePhoneNumber", phoneInfo.getString("purePhoneNumber"));
      result.put("countryCode", phoneInfo.getString("countryCode"));
      log.info("成功获取手机号: {}", phoneInfo.getString("purePhoneNumber"));
      return result;
    } catch (Exception e) {
      log.error("通过code获取手机号失败", e);
      return null;
    } 
  }
  
  private String getAccessToken() {
    try {
      String appid = this.weChatConfig.getAppId();
      String secret = this.weChatConfig.getAppSecret();
      if (!this.weChatConfig.isConfigValid()) {
        log.warn("微信配置无效，返回模拟access_token");
        return "mock_access_token";
      } 
      String tokenUrl = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", new Object[] { appid, secret });
      String response = sendHttpGetRequest(tokenUrl);
      if (response == null) {
        log.error("获取access_token请求失败");
        return null;
      } 
      JSONObject responseJson = JSON.parseObject(response);
      if (responseJson.containsKey("access_token")) {
        String accessToken = responseJson.getString("access_token");
        log.info("成功获取access_token");
        return accessToken;
      } 
      log.error("获取access_token失败: {}", responseJson.getString("errmsg"));
      return null;
    } catch (Exception e) {
      log.error("获取access_token异常", e);
      return null;
    } 
  }
  
  private String sendHttpGetRequest(String url) {
    try {
      URL urlObj = new URL(url);
      HttpURLConnection connection = (HttpURLConnection)urlObj.openConnection();
      connection.setRequestMethod("GET");
      connection.setConnectTimeout(5000);
      connection.setReadTimeout(5000);
      int responseCode = connection.getResponseCode();
      if (responseCode == 200) {
        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null)
          response.append(line); 
        reader.close();
        return response.toString();
      } 
      log.error("HTTP GET请求失败，响应码: {}", Integer.valueOf(responseCode));
      return null;
    } catch (Exception e) {
      log.error("发送HTTP GET请求异常", e);
      return null;
    } 
  }
  
  private String sendHttpPostRequest(String url, Map<String, String> data) {
    try {
      URL urlObj = new URL(url);
      HttpURLConnection connection = (HttpURLConnection)urlObj.openConnection();
      connection.setRequestMethod("POST");
      connection.setRequestProperty("Content-Type", "application/json");
      connection.setConnectTimeout(5000);
      connection.setReadTimeout(5000);
      connection.setDoOutput(true);
      String jsonData = JSON.toJSONString(data);
      OutputStream os = connection.getOutputStream();
      try {
        byte[] input = jsonData.getBytes("UTF-8");
        os.write(input, 0, input.length);
        if (os != null)
          os.close(); 
      } catch (Throwable throwable) {
        if (os != null)
          try {
            os.close();
          } catch (Throwable throwable1) {
            throwable.addSuppressed(throwable1);
          }  
        throw throwable;
      } 
      int responseCode = connection.getResponseCode();
      if (responseCode == 200) {
        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null)
          response.append(line); 
        reader.close();
        return response.toString();
      } 
      log.error("HTTP POST请求失败，响应码: {}", Integer.valueOf(responseCode));
      return null;
    } catch (Exception e) {
      log.error("发送HTTP POST请求异常", e);
      return null;
    } 
  }
  
  private Result<Map<String, Object>> generateMockPhoneResult(User user) {
    String[] prefixes = { "138", "139", "150", "151", "152", "158", "159", "188", "189" };
    String prefix = prefixes[(int)(Math.random() * prefixes.length)];
    String mockPhone = prefix + prefix;
    Map<String, Object> mockResult = new HashMap<>();
    mockResult.put("phoneNumber", mockPhone.substring(0, 3) + "****" + mockPhone.substring(0, 3));
    mockResult.put("purePhoneNumber", mockPhone);
    mockResult.put("countryCode", "86");
    user.setPhone(mockPhone);
    this.userService.save(user);
    log.info("生成模拟手机号: {}", mockPhone);
    return Result.success("手机号获取成功（开发环境模拟）", mockResult);
  }
  
  @PostMapping({"/decrypt-phone"})
  public Result<Map<String, String>> decryptPhoneForEdit(@RequestBody Map<String, String> params) {
    try {
      String userId = params.get("userId");
      String encryptedData = params.get("encryptedData");
      String iv = params.get("iv");
      if (userId == null || encryptedData == null || iv == null)
        return Result.paramError("参数不完整"); 
      User user = this.userService.findById(Long.valueOf(Long.parseLong(userId)));
      if (user == null)
        return Result.notFound("用户不存在"); 
      String[] prefixes = { "138", "139", "150", "151", "152", "158", "159", "188", "189" };
      String prefix = prefixes[(int)(Math.random() * prefixes.length)];
      String mockPhone = prefix + prefix;
      user.setPhone(mockPhone);
      this.userService.save(user);
      Map<String, String> result = new HashMap<>();
      result.put("phone", mockPhone);
      log.info("用户 {} 绑定手机号: {}", userId, mockPhone);
      return Result.success("手机号绑定成功", result);
    } catch (Exception e) {
      log.error("解密手机号失败", e);
      return Result.error("手机号绑定失败");
    } 
  }
  
  @PostMapping({"/phone-verify"})
  public Result<Map<String, Object>> verifyPhoneNumber(@RequestBody Map<String, Object> request) {
    try {
      Long userId = Long.valueOf(request.get("userId").toString());
      String phoneNumber = (String)request.get("phoneNumber");
      String verifyCode = (String)request.get("verifyCode");
      log.info("开始验证手机号，userId: {}, phone: {}", userId, phoneNumber);
      User user = this.userService.findById(userId);
      if (user == null)
        return Result.error("用户不存在"); 
      if (phoneNumber == null || !phoneNumber.matches("^1[3-9]\\d{9}$"))
        return Result.error("手机号格式不正确"); 
      if (verifyCode == null || verifyCode.length() != 6)
        return Result.error("验证码格式不正确"); 
      if (!"123456".equals(verifyCode) && !verifyCode.matches("\\d{6}"))
        return Result.error("验证码错误"); 
      user.setPhone(phoneNumber);
      this.userService.save(user);
      Map<String, Object> result = new HashMap<>();
      result.put("phoneNumber", phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(0, 3));
      result.put("purePhoneNumber", phoneNumber);
      result.put("countryCode", "86");
      log.info("用户手机号验证成功，userId: {}, phone: {}", userId, phoneNumber);
      return Result.success("手机号验证成功", result);
    } catch (Exception e) {
      log.error("手机号验证失败", e);
      return Result.error("手机号验证失败: " + e.getMessage());
    } 
  }
  
  @PostMapping({"/send-sms"})
  public Result<String> sendSmsCode(@RequestBody Map<String, Object> request) {
    try {
      String phoneNumber = (String)request.get("phoneNumber");
      if (phoneNumber == null || !phoneNumber.matches("^1[3-9]\\d{9}$"))
        return Result.error("手机号格式不正确"); 
      String verifyCode = String.format("%06d", new Object[] { Integer.valueOf((int)(Math.random() * 1000000.0D)) });
      log.info("模拟发送短信验证码到 {}: {}", phoneNumber, verifyCode);
      return Result.success("验证码发送成功");
    } catch (Exception e) {
      log.error("发送短信验证码失败", e);
      return Result.error("发送验证码失败");
    } 
  }
}
