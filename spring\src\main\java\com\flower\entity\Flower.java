package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName("flowers")
public class Flower {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private String name;
  
  private String description;
  
  private BigDecimal price;
  
  private BigDecimal originalPrice;
  
  private Long categoryId;
  
  private Integer stockQuantity;
  
  private Integer salesCount;
  
  private String mainImage;
  
  private String images;
  
  private String tags;
  
  private String flowerLanguage;
  
  private String careInstructions;
  
  private String occasion;
  
  private String color;
  
  private String size;
  
  private Integer isFeatured;
  
  private Integer status;
  
  private LocalDateTime createdAt;
  
  private LocalDateTime updatedAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public void setDescription(String description) {
    this.description = description;
  }
  
  public void setPrice(BigDecimal price) {
    this.price = price;
  }
  
  public void setOriginalPrice(BigDecimal originalPrice) {
    this.originalPrice = originalPrice;
  }
  
  public void setCategoryId(Long categoryId) {
    this.categoryId = categoryId;
  }
  
  public void setStockQuantity(Integer stockQuantity) {
    this.stockQuantity = stockQuantity;
  }
  
  public void setSalesCount(Integer salesCount) {
    this.salesCount = salesCount;
  }
  
  public void setMainImage(String mainImage) {
    this.mainImage = mainImage;
  }
  
  public void setImages(String images) {
    this.images = images;
  }
  
  public void setTags(String tags) {
    this.tags = tags;
  }
  
  public void setFlowerLanguage(String flowerLanguage) {
    this.flowerLanguage = flowerLanguage;
  }
  
  public void setCareInstructions(String careInstructions) {
    this.careInstructions = careInstructions;
  }
  
  public void setOccasion(String occasion) {
    this.occasion = occasion;
  }
  
  public void setColor(String color) {
    this.color = color;
  }
  
  public void setSize(String size) {
    this.size = size;
  }
  
  public void setIsFeatured(Integer isFeatured) {
    this.isFeatured = isFeatured;
  }
  
  public void setStatus(Integer status) {
    this.status = status;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
  
  public String toString() {
    return "Flower(id=" + getId() + ", name=" + getName() + ", description=" + getDescription() + ", price=" + getPrice() + ", originalPrice=" + getOriginalPrice() + ", categoryId=" + getCategoryId() + ", stockQuantity=" + getStockQuantity() + ", salesCount=" + getSalesCount() + ", mainImage=" + getMainImage() + ", images=" + getImages() + ", tags=" + getTags() + ", flowerLanguage=" + getFlowerLanguage() + ", careInstructions=" + getCareInstructions() + ", occasion=" + getOccasion() + ", color=" + getColor() + ", size=" + getSize() + ", isFeatured=" + getIsFeatured() + ", status=" + getStatus() + ", createdAt=" + getCreatedAt() + ", updatedAt=" + getUpdatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.Flower))
      return false; 
    com.flower.entity.Flower other = (com.flower.entity.Flower)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$categoryId = getCategoryId(), other$categoryId = other.getCategoryId();
    if ((this$categoryId == null) ? (other$categoryId != null) : !this$categoryId.equals(other$categoryId))
      return false; 
    Object this$stockQuantity = getStockQuantity(), other$stockQuantity = other.getStockQuantity();
    if ((this$stockQuantity == null) ? (other$stockQuantity != null) : !this$stockQuantity.equals(other$stockQuantity))
      return false; 
    Object this$salesCount = getSalesCount(), other$salesCount = other.getSalesCount();
    if ((this$salesCount == null) ? (other$salesCount != null) : !this$salesCount.equals(other$salesCount))
      return false; 
    Object this$isFeatured = getIsFeatured(), other$isFeatured = other.getIsFeatured();
    if ((this$isFeatured == null) ? (other$isFeatured != null) : !this$isFeatured.equals(other$isFeatured))
      return false; 
    Object this$status = getStatus(), other$status = other.getStatus();
    if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status))
      return false; 
    Object this$name = getName(), other$name = other.getName();
    if ((this$name == null) ? (other$name != null) : !this$name.equals(other$name))
      return false; 
    Object this$description = getDescription(), other$description = other.getDescription();
    if ((this$description == null) ? (other$description != null) : !this$description.equals(other$description))
      return false; 
    Object this$price = getPrice(), other$price = other.getPrice();
    if ((this$price == null) ? (other$price != null) : !this$price.equals(other$price))
      return false; 
    Object this$originalPrice = getOriginalPrice(), other$originalPrice = other.getOriginalPrice();
    if ((this$originalPrice == null) ? (other$originalPrice != null) : !this$originalPrice.equals(other$originalPrice))
      return false; 
    Object this$mainImage = getMainImage(), other$mainImage = other.getMainImage();
    if ((this$mainImage == null) ? (other$mainImage != null) : !this$mainImage.equals(other$mainImage))
      return false; 
    Object this$images = getImages(), other$images = other.getImages();
    if ((this$images == null) ? (other$images != null) : !this$images.equals(other$images))
      return false; 
    Object this$tags = getTags(), other$tags = other.getTags();
    if ((this$tags == null) ? (other$tags != null) : !this$tags.equals(other$tags))
      return false; 
    Object this$flowerLanguage = getFlowerLanguage(), other$flowerLanguage = other.getFlowerLanguage();
    if ((this$flowerLanguage == null) ? (other$flowerLanguage != null) : !this$flowerLanguage.equals(other$flowerLanguage))
      return false; 
    Object this$careInstructions = getCareInstructions(), other$careInstructions = other.getCareInstructions();
    if ((this$careInstructions == null) ? (other$careInstructions != null) : !this$careInstructions.equals(other$careInstructions))
      return false; 
    Object this$occasion = getOccasion(), other$occasion = other.getOccasion();
    if ((this$occasion == null) ? (other$occasion != null) : !this$occasion.equals(other$occasion))
      return false; 
    Object this$color = getColor(), other$color = other.getColor();
    if ((this$color == null) ? (other$color != null) : !this$color.equals(other$color))
      return false; 
    Object this$size = getSize(), other$size = other.getSize();
    if ((this$size == null) ? (other$size != null) : !this$size.equals(other$size))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    if ((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt))
      return false; 
    Object this$updatedAt = getUpdatedAt(), other$updatedAt = other.getUpdatedAt();
    return !((this$updatedAt == null) ? (other$updatedAt != null) : !this$updatedAt.equals(other$updatedAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.Flower;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $categoryId = getCategoryId();
    result = result * 59 + (($categoryId == null) ? 43 : $categoryId.hashCode());
    Object $stockQuantity = getStockQuantity();
    result = result * 59 + (($stockQuantity == null) ? 43 : $stockQuantity.hashCode());
    Object $salesCount = getSalesCount();
    result = result * 59 + (($salesCount == null) ? 43 : $salesCount.hashCode());
    Object $isFeatured = getIsFeatured();
    result = result * 59 + (($isFeatured == null) ? 43 : $isFeatured.hashCode());
    Object $status = getStatus();
    result = result * 59 + (($status == null) ? 43 : $status.hashCode());
    Object $name = getName();
    result = result * 59 + (($name == null) ? 43 : $name.hashCode());
    Object $description = getDescription();
    result = result * 59 + (($description == null) ? 43 : $description.hashCode());
    Object $price = getPrice();
    result = result * 59 + (($price == null) ? 43 : $price.hashCode());
    Object $originalPrice = getOriginalPrice();
    result = result * 59 + (($originalPrice == null) ? 43 : $originalPrice.hashCode());
    Object $mainImage = getMainImage();
    result = result * 59 + (($mainImage == null) ? 43 : $mainImage.hashCode());
    Object $images = getImages();
    result = result * 59 + (($images == null) ? 43 : $images.hashCode());
    Object $tags = getTags();
    result = result * 59 + (($tags == null) ? 43 : $tags.hashCode());
    Object $flowerLanguage = getFlowerLanguage();
    result = result * 59 + (($flowerLanguage == null) ? 43 : $flowerLanguage.hashCode());
    Object $careInstructions = getCareInstructions();
    result = result * 59 + (($careInstructions == null) ? 43 : $careInstructions.hashCode());
    Object $occasion = getOccasion();
    result = result * 59 + (($occasion == null) ? 43 : $occasion.hashCode());
    Object $color = getColor();
    result = result * 59 + (($color == null) ? 43 : $color.hashCode());
    Object $size = getSize();
    result = result * 59 + (($size == null) ? 43 : $size.hashCode());
    Object $createdAt = getCreatedAt();
    result = result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
    Object $updatedAt = getUpdatedAt();
    return result * 59 + (($updatedAt == null) ? 43 : $updatedAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public String getName() {
    return this.name;
  }
  
  public String getDescription() {
    return this.description;
  }
  
  public BigDecimal getPrice() {
    return this.price;
  }
  
  public BigDecimal getOriginalPrice() {
    return this.originalPrice;
  }
  
  public Long getCategoryId() {
    return this.categoryId;
  }
  
  public Integer getStockQuantity() {
    return this.stockQuantity;
  }
  
  public Integer getSalesCount() {
    return this.salesCount;
  }
  
  public String getMainImage() {
    return this.mainImage;
  }
  
  public String getImages() {
    return this.images;
  }
  
  public String getTags() {
    return this.tags;
  }
  
  public String getFlowerLanguage() {
    return this.flowerLanguage;
  }
  
  public String getCareInstructions() {
    return this.careInstructions;
  }
  
  public String getOccasion() {
    return this.occasion;
  }
  
  public String getColor() {
    return this.color;
  }
  
  public String getSize() {
    return this.size;
  }
  
  public Integer getIsFeatured() {
    return this.isFeatured;
  }
  
  public Integer getStatus() {
    return this.status;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
  
  public LocalDateTime getUpdatedAt() {
    return this.updatedAt;
  }
}
