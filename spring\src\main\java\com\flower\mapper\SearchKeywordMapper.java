package com.flower.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flower.entity.SearchKeyword;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SearchKeywordMapper extends BaseMapper<SearchKeyword> {
  @Select({"SELECT * FROM search_keywords ORDER BY search_count DESC, updated_at DESC LIMIT #{limit}"})
  List<SearchKeyword> topKeywords(@Param("limit") int paramInt);
}
