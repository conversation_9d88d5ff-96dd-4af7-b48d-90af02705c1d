package com.flower.controller;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/image/swiper"})
@CrossOrigin
public class SwiperImageController {
  @GetMapping({"/{filename:.+}"})
  public ResponseEntity<Resource> getSwiperImage(@PathVariable String filename) {
    try {
      String projectPath = System.getProperty("user.dir");
      String imagePath = projectPath + "/image/swiper/" + projectPath;
      System.out.println("请求轮播图: " + filename);
      System.out.println("完整路径: " + imagePath);
      File imageFile = new File(imagePath);
      if (!imageFile.exists()) {
        System.out.println("文件不存在: " + imagePath);
        return ResponseEntity.notFound().build();
      } 
      FileSystemResource fileSystemResource = new FileSystemResource(imageFile);
      String contentType = "application/octet-stream";
      try {
        Path path = Paths.get(imagePath, new String[0]);
        contentType = Files.probeContentType(path);
        if (contentType == null) {
          String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
          switch (extension) {
            case "jpg":
            case "jpeg":
              contentType = "image/jpeg";
              break;
            case "png":
              contentType = "image/png";
              break;
            case "gif":
              contentType = "image/gif";
              break;
            default:
              contentType = "application/octet-stream";
              break;
          } 
        } 
      } catch (Exception e) {
        System.out.println("无法确定内容类型: " + e.getMessage());
      } 
      System.out.println("返回图片，内容类型: " + contentType);
      return ((ResponseEntity.BodyBuilder)ResponseEntity.ok()
        .contentType(MediaType.parseMediaType(contentType))
        .header("Content-Disposition", new String[] { "inline; filename=\"" + filename + "\"" })).body(fileSystemResource);
    } catch (Exception e) {
      System.err.println("获取轮播图失败: " + e.getMessage());
      e.printStackTrace();
      return ResponseEntity.internalServerError().build();
    } 
  }
}
