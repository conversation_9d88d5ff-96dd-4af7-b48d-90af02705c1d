package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName("order_items")
public class OrderItem {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  
  private Long orderId;
  
  private Long flowerId;
  
  private String flowerName;
  
  private String flowerImage;
  
  private BigDecimal price;
  
  private Integer quantity;
  
  private BigDecimal subtotal;
  
  private LocalDateTime createdAt;
  
  public void setId(Long id) {
    this.id = id;
  }
  
  public void setOrderId(Long orderId) {
    this.orderId = orderId;
  }
  
  public void setFlowerId(Long flowerId) {
    this.flowerId = flowerId;
  }
  
  public void setFlowerName(String flowerName) {
    this.flowerName = flowerName;
  }
  
  public void setFlowerImage(String flowerImage) {
    this.flowerImage = flowerImage;
  }
  
  public void setPrice(BigDecimal price) {
    this.price = price;
  }
  
  public void setQuantity(Integer quantity) {
    this.quantity = quantity;
  }
  
  public void setSubtotal(BigDecimal subtotal) {
    this.subtotal = subtotal;
  }
  
  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
  
  public String toString() {
    return "OrderItem(id=" + getId() + ", orderId=" + getOrderId() + ", flowerId=" + getFlowerId() + ", flowerName=" + getFlowerName() + ", flowerImage=" + getFlowerImage() + ", price=" + getPrice() + ", quantity=" + getQuantity() + ", subtotal=" + getSubtotal() + ", createdAt=" + getCreatedAt() + ")";
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.flower.entity.OrderItem))
      return false; 
    com.flower.entity.OrderItem other = (com.flower.entity.OrderItem)o;
    if (!other.canEqual(this))
      return false; 
    Object this$id = getId(), other$id = other.getId();
    if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
      return false; 
    Object this$orderId = getOrderId(), other$orderId = other.getOrderId();
    if ((this$orderId == null) ? (other$orderId != null) : !this$orderId.equals(other$orderId))
      return false; 
    Object this$flowerId = getFlowerId(), other$flowerId = other.getFlowerId();
    if ((this$flowerId == null) ? (other$flowerId != null) : !this$flowerId.equals(other$flowerId))
      return false; 
    Object this$quantity = getQuantity(), other$quantity = other.getQuantity();
    if ((this$quantity == null) ? (other$quantity != null) : !this$quantity.equals(other$quantity))
      return false; 
    Object this$flowerName = getFlowerName(), other$flowerName = other.getFlowerName();
    if ((this$flowerName == null) ? (other$flowerName != null) : !this$flowerName.equals(other$flowerName))
      return false; 
    Object this$flowerImage = getFlowerImage(), other$flowerImage = other.getFlowerImage();
    if ((this$flowerImage == null) ? (other$flowerImage != null) : !this$flowerImage.equals(other$flowerImage))
      return false; 
    Object this$price = getPrice(), other$price = other.getPrice();
    if ((this$price == null) ? (other$price != null) : !this$price.equals(other$price))
      return false; 
    Object this$subtotal = getSubtotal(), other$subtotal = other.getSubtotal();
    if ((this$subtotal == null) ? (other$subtotal != null) : !this$subtotal.equals(other$subtotal))
      return false; 
    Object this$createdAt = getCreatedAt(), other$createdAt = other.getCreatedAt();
    return !((this$createdAt == null) ? (other$createdAt != null) : !this$createdAt.equals(other$createdAt));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.flower.entity.OrderItem;
  }
  
  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $id = getId();
    result = result * 59 + (($id == null) ? 43 : $id.hashCode());
    Object $orderId = getOrderId();
    result = result * 59 + (($orderId == null) ? 43 : $orderId.hashCode());
    Object $flowerId = getFlowerId();
    result = result * 59 + (($flowerId == null) ? 43 : $flowerId.hashCode());
    Object $quantity = getQuantity();
    result = result * 59 + (($quantity == null) ? 43 : $quantity.hashCode());
    Object $flowerName = getFlowerName();
    result = result * 59 + (($flowerName == null) ? 43 : $flowerName.hashCode());
    Object $flowerImage = getFlowerImage();
    result = result * 59 + (($flowerImage == null) ? 43 : $flowerImage.hashCode());
    Object $price = getPrice();
    result = result * 59 + (($price == null) ? 43 : $price.hashCode());
    Object $subtotal = getSubtotal();
    result = result * 59 + (($subtotal == null) ? 43 : $subtotal.hashCode());
    Object $createdAt = getCreatedAt();
    return result * 59 + (($createdAt == null) ? 43 : $createdAt.hashCode());
  }
  
  public Long getId() {
    return this.id;
  }
  
  public Long getOrderId() {
    return this.orderId;
  }
  
  public Long getFlowerId() {
    return this.flowerId;
  }
  
  public String getFlowerName() {
    return this.flowerName;
  }
  
  public String getFlowerImage() {
    return this.flowerImage;
  }
  
  public BigDecimal getPrice() {
    return this.price;
  }
  
  public Integer getQuantity() {
    return this.quantity;
  }
  
  public BigDecimal getSubtotal() {
    return this.subtotal;
  }
  
  public LocalDateTime getCreatedAt() {
    return this.createdAt;
  }
}
